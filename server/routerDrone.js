import express from 'express';

import { djiMediaFileRouter } from './api/resources/DJICloud/MediaFile/mediaFile.dji.router';
import { djiWaylineRouter } from './api/resources/DJICloud/Wayline/wayline.dji.router';
import { djiStorageRouter } from './api/resources/DJICloud/ObjectStorage/objectStorage.dji.router';
import { djiManageRouter } from './api/resources/DJICloud/Manage/manage.dji.router';

const routerDrone = express.Router();

routerDrone.use('/media/api/v1', djiMediaFileRouter);
routerDrone.use('/wayline/api/v1', djiWaylineRouter);
routerDrone.use('/storage/api/v1', djiStorageRouter);
routerDrone.use('/manage/api/v1', djiManageRouter);

module.exports = routerDrone;

