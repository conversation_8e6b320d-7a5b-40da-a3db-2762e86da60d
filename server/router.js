import express from 'express';

import userRouter from './api/resources/User/user.router';
import refreshTokenRouter from './api/resources/RefreshToken/refreshToken.router';

import { donViRouter } from './api/resources/DonVi/donVi.router';
import { viTriRouter } from './api/resources/TongKe/ViTri/viTri.router';
import { loaiViTriRouter } from './api/resources/DanhMuc/LoaiViTri/loaiViTri.router';
import { nhanVienRouter } from './api/resources/DanhMuc/NhanVien/nhanVien.router';
import { hangMucRouter } from './api/resources/DanhMuc/HangMuc/hangMuc.router';

import { duongDayChungRouter } from './api/resources/TongKe/DuongDayChung/duongDayChung.router';
import { duongDayRouter } from './api/resources/TongKe/DuongDay/duongDay.router';
import { loaiDuongDayRouter } from './api/resources/DanhMuc/LoaiDuongDay/loaiDuongDay.router';
import { congTrinhRouter } from './api/resources/TongKe/CongTrinh/congTrinh.router';
import { giaoCheoRouter } from './api/resources/TongKe/GiaoCheo/giaoCheo.router';
import { dayChongSetRouter } from './api/resources/TongKe/DayChongSet/dayChongSet.router';
import { dayCapQuangRouter } from './api/resources/TongKe/DayCapQuang/dayCapQuang.router';
import { cotDienRouter } from './api/resources/TongKe/CotDien/cotDien.router';
import { tiepDatRouter } from './api/resources/TongKe/TiepDat/tiepDat.router';

import { hanhChinhRouter } from './api/resources/DanhMuc/HanhChinh/hanhChinh.router';
import { tinhThanhRouter } from './api/resources/DanhMuc/HanhChinh/TinhThanh/tinhThanh.router';
import { quanHuyenRouter } from './api/resources/DanhMuc/HanhChinh/QuanHuyen/quanHuyen.router';
import { phuongXaRouter } from './api/resources/DanhMuc/HanhChinh/PhuongXa/phuongXa.router';

import { loaiCotRouter } from './api/resources/DanhMuc/LoaiCot/loaiCot.router';
import { diaHinhRouter } from './api/resources/DanhMuc/DiaHinh/diaHinh.router';
import { loaiMongRouter } from './api/resources/DanhMuc/LoaiMong/loaiMong.router';

import { loaiCachDienRouter } from './api/resources/DanhMuc/CachDien/loaiCachDienRouter';

import { loaiTiepDatRouter } from './api/resources/DanhMuc/LoaiTiepDat/loaiTiepDat.router';
import { buLongNeoMongRouter } from './api/resources/DanhMuc/BuLongNeoMong/buLongNeoMong.router';
import { loaiDayDanRouter } from './api/resources/DanhMuc/DayDan/loaiDayDanRouter';
import { loaiDayCapQuangRouter } from './api/resources/DanhMuc/LoaiDayCapQuang/loaiDayCapQuang.router';
import { loaiDayChongSetRouter } from './api/resources/DanhMuc/LoaiDayChongSet/loaiDayChongSet.router';
import { chongRungRouter } from './api/resources/DanhMuc/ChongRung/chongRung.router';
import { khungDinhViRouter } from './api/resources/DanhMuc/KhungDinhVi/khungDinhVi.router';
import { taBuRouter } from './api/resources/DanhMuc/TaBu/taBu.router';
import { vanHanhRouter } from './api/resources/TongKe/VanHanh/vanHanh.router';
import { dayDanRouter } from './api/resources/TongKe/DayDan/dayDan.router';
import { cachDienRouter } from './api/resources/TongKe/CachDien/cachDien.router';
import { thietBiRouter } from './api/resources/TongKe/ThietBi/thietBi.router';
import { phieuGiaoViecRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.router';
import { importPhieuGiaoViecRouter } from './api/resources/TongKe/ImportPhieuGiaoViec/importPhieuGiaoViec.router';
import { phieuMauRouter } from './api/resources/QuanLyVanHanh/PhieuMau/phieuMau.router';
import { nguoiCongTacRouter } from './api/resources/QuanLyVanHanh/NguoiCongTac/nguoiCongTac.router';
import { danhMucCongViecRouter } from './api/resources/DanhMuc/DanhMucCongViec/danhMucCongViec.router';
import { danhMucCongViecPhuTroRouter } from './api/resources/DanhMuc/DanhMucCongViecPhuTro/danhMucCongViecPhuTro.router';
import { bienPhapAnToanRouter } from './api/resources/DanhMuc/BienPhapAnToan/bienPhapAnToan.router';
import { banDoRouter } from './api/resources/BanDo/banDo.router';
import { dieuKienAnToanRouter } from './api/resources/DanhMuc/DieuKienAnToan/dieuKienAnToan.router';
import { roleRouter } from './api/resources/Role/role.router';
import { tieuChiRouter } from './api/resources/DanhMuc/NoiDungKiemTra/TieuChi/tieuChi.router';
import { noiDungRouter } from './api/resources/DanhMuc/NoiDungKiemTra/noiDung.router';
import { doDienTroRouter } from './api/resources/QuanLyVanHanh/DoDienTro/doDienTro.router';
import { ketQuaKiemTraRouter } from './api/resources/QuanLyVanHanh/KetQuaKiemTra/ketQuaKiemTra.router';
import { ketQuaSuaChuaKhongKeHoachRouter } from './api/resources/QuanLyVanHanh/KetQuaSuaChuaKhongKeHoach/ketQuaSuaChuaKhongKeHoach.router';
import { caiDatHeThongRouter } from './api/resources/CaiDatHeThong/caiDatHeThong.router';
import { caiDatPmisRouter } from './api/resources/CaiDatPmis/caiDatPmis.router';
import { anhViTriRouter } from './api/resources/AnhViTri/anhViTri.router';
import { fileRouter } from './api/resources/File/file.router';
import { dmThietBiRouter } from './api/resources/DanhMuc/DmThietBi/dmThietBi.router';
import { thietBiPhatHienRouter } from './api/resources/QuanLyVanHanh/ThietBiPhatHien/thietBiPhatHien.router';
import { batThuongPhatHienRouter } from './api/resources/QuanLyVanHanh/BatThuongPhatHien/batThuongPhatHien.router';
import { trangThaiPhieuGiaoViec } from './api/resources/DanhMuc/TrangThaiCongViec/trangThaiPhieuGiaoViec.router';
import { ketQuaDoNhietDoRouter } from './api/resources/KetQuaDoNhietDo/ketQuaDoNhietDo.router';
import { ketQuaDoCorocamRouter } from './api/resources/QuanLyVanHanh/KetQuaDoCorocam/ketQuaDoCorocam.router';
import { ketQuaDoNhietDoCompositeRouter } from './api/resources/QuanLyVanHanh/KetQuaDoNhietDoComposite/ketQuaDoNhietDoComposite.router';
import { ketQuaDoCuongDoDienTruongRouter } from './api/resources/QuanLyVanHanh/KetQuaDoCuongDoDienTruong/ketQuaDoCuongDoDienTruong.router';
import { ketQuaDoKhoangCachGiaoCheoRouter } from './api/resources/QuanLyVanHanh/KetQuaDoKhoangCachGiaoCheo/ketQuaDoKhoangCachGiaoCheo.router';
import { congViecPhuTroRouter } from './api/resources/QuanLyVanHanh/CongViecPhuTro/congViecPhuTro.router';

import { taiLieuRouter } from './api/resources/TaiLieu/taiLieu.router';
import { linkFileRouter } from './api/resources/QuanLyVanHanh/HoSo/LinkFile/linkFile.router';
import { loaiCongViecRouter } from './api/resources/DanhMuc/LoaiCongViec/loaiCongViec.router';

import { danhMucDroneRouter } from './api/resources/QuanLyDrone/DanhMucDrone/danhMucDrone.router';
import { droneRouter } from './api/resources/QuanLyDrone/Drone/drone.router';
import { phuKienRouter } from './api/resources/QuanLyDrone/Drone/PhuKien/phuKien.router';
import { notificationRouter } from './api/resources/Notification/notification.router';
import { remindRouter } from './api/resources/Remind/remind.router';
import { reportRouter } from './api/resources/Report/report.router';
import { reportNSLVRouter } from './api/resources/Report/ReportNSLV/reportNSLV.router';
import { dashboardRouter } from './api/resources/Dashboard/dashboard.router';
import { phieuKiemTraRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/PhieuKiemTra/phieuKiemTra.router';
import { phieuSuaChuaRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/PhieuSuaChua/phieuSuaChuaRouter';
import { phieuDoLuongRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/PhieuDoLuong/phieuDoLuongRouter';
import { phieuCongTacPhuTroRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/PhieuCongTacPhuTro/phieuCongTacPhuTro.router';
import { tinhHinhTonTaiRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/TinhHinhTonTai/tinhHinhTonTai.router';
import { hieuChinhTonTaiRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/HieuChinhTonTai/hieuChinhTonTai.router';
import { caiDatAiRouter } from './api/resources/CaiDatAi/caiDatAi.router';
import { caiDatVanHanhRouter } from './api/resources/CaiDatVanHanh/caiDatVanHanh.router';
import { mayDoRouter } from './api/resources/QuanLyMayDo/MayDo/mayDo.router';
import { phanBoMayDoRouter } from './api/resources/QuanLyMayDo/PhanBoMayDo/phanBoMayDo.router';
import { thongTinQuanLyRouter } from './api/resources/ThongTinQuanLy/thongTinQuanLy.router';
import { tonTaiRouter } from './api/resources/QuanLyVanHanh/TonTai/tonTai.router';
import { ketQuaSuaChuaCoKeHoachRouter } from './api/resources/QuanLyVanHanh/KetQuaSuaChuaCoKeHoach/ketQuaSuaChuaCoKeHoach.router';
import { ketQuaDoKhoangCachPhaDatRouter } from './api/resources/QuanLyVanHanh/KetQuaDoKhoangCachPhaDat/ketQuaDoKhoangCachPhaDat.router';
import { khoangCotRouter } from './api/resources/TongKe/KhoangCot/khoangCot.router';
import { khoangNeoRouter } from './api/resources/TongKe/KhoangNeo/khoangNeo.router';
import { hanhLangTuyenRouter } from './api/resources/TongKe/HanhLangTuyen/hanhLangTuyen.router';
import { tongKeMoiNoiRouter } from './api/resources/TongKe/TongKeMoiNoi/tongKeMoiNoi.router';
import { congViecPhatSinhRouter } from './api/resources/QuanLyVanHanh/CongViecPhatSinh/congViecPhatSinh.router';
import { truongDuLieuRouter } from './api/resources/TruongDuLieu/truongDuLieu.router';
import { hoSoThietBiRouter } from './api/resources/HoSoThietBi/hoSoThietBi.router';
import { suCoDuongDayRouter } from './api/resources/TongKe/SuCoDuongDay/suCoDuongDay.router';
import { tonTaiCapTrenRouter } from './api/resources/QuanLyVanHanh/PhieuGiaoViec/TonTaiCapTren/tonTaiCapTren.router';

import { nhomThietBiDungCuRouter } from './api/resources/ThietBiDungCu/nhomThietBiDungCu.router';
import { thietBiDungCuRouter } from './api/resources/ThietBiDungCu/chitiet/thietBiDungCu.router';
import { soTheoDoiThietBiRouter } from './api/resources/ThietBiDungCu/SoTheoDoi/soTheoDoi.router';

import { tapTinRouter } from './api/resources/QuanLyVanHanh/TapTin/tapTin.router';
import { hoSoRouter } from './api/resources/QuanLyVanHanh/HoSo/hoSo.router';
import { viTriCongViecRouter } from './api/resources/QuanLyVanHanh/ViTriCongViec/viTriCongViec.router';

import { tonTaiThoiDiemBanGiaoRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/TonTaiThoiDiemBanGiao/tonTaiThoiDiemBanGiao.router';
import { tonTaiCongTrinhXayDungRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/tonTaiCongTrinhXayDung.router';
import { bienBanRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/BienBanDongDien/bienBan.router';
import { chiTietBienBanRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/BienBanDongDien/ChiTietBienBan/chiTietBienBan.router';
import { fileBienBanRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/FileBienBan/fileBienBan.router';
import { anhTonTaiRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/AnhTonTai/anhTonTai.router';
import { tonTaiLamQuangRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/TonTaiLamQuang/tonTaiLamQuang.router';
import { cauHinhBayRouter } from './api/resources/CauHinhBay/cau_hinh_bay.router';

import { khiemKhuyetThietBiRouter } from './api/resources/QuanLyKhiemKhuyetThietBi/KhiemKhuyetThietBi/khiemKhuyetThietBi.router';
import { reportKKTBRouter } from './api/resources/QuanLyKhiemKhuyetThietBi/ReportKKTB/reportKKTB.router';
import { missionPointRouter } from './api/resources/MissionPoint/mission_point.router';

import { tieuChiLamQuangRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/NoiDungLamQuang/TieuChiLamQuang/tieuChiLamQuang.router';
import { noiDungLamQuangRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/NoiDungLamQuang/noiDungLamQuang.router';

import { phuLucTonTaiRouter } from './api/resources/QuanLyTonTaiCongTrinhXayDung/PhuLucTonTai/phuLucTonTai.router';
import { importCongTrinhRouter } from './api/resources/TongKe/ImportCongTrinh/importCongTrinh.router';
import { importDuongDayRouter } from './api/resources/TongKe/ImportDuongDay/importDuongDay.router';
import { ntHanhLangTuyenRouter } from './api/resources/TongKe/NghiemThuHanhLangTuyen/nghiemThuHanhLangTuyen.router';

import { reportDoPhaDatRouter } from './api/resources/Report/ReportDoPhaDat/doPhaDat.router';
import { reportDoDienTroRouter } from './api/resources/Report/ReportDoDienTro/doDienTro.router';
import { reportDoNhietDoRouter } from './api/resources/Report/ReportDoNhietDo/doNhietDo.router';
import { reportPhieuGiaoViecRouter } from './api/resources/Report/ReportPhieuGiaoViec/reportPhieuGiaoViec.router';
import { dongBoRouter } from './api/resources/Report/DongBoDuLieuDo/dongBo.router';

import { chuyenDeRouter } from './api/resources/DanhMuc/ChuyenDe/chuyenDe.router';
import { ndktDroneRouter } from './api/resources/DanhMuc/NoiDungKiemTraDrone/noiDungKiemTraDrone.router';
import { syncPmisRouter } from './api/resources/TongKe/SyncPmis/syncPmis.router';
import { imageLogRouter } from './api/resources/AnhViTri/ImageLog/imageLog.router';
import { serverLogRouter } from './api/resources/Log/ServerLog/serverLog.router';
import { logGopDuongDayRouter } from './api/resources/Log/LogGopDuongDay/logGopDuongDay.router';

import { lichSuDoNhietDoRouter } from './api/resources/KetQuaDoNhietDo/LichSuDoNhietDo/lichSuDoNhietDo.router';

import { raSoatDuLieuRouter } from './api/resources/RaSoatDuLieu/raSoatDuLieu.router';

import { gpsUserRouter } from './api/resources/GpsUser/gpsUser.router';
import { supportRouter } from './api/resources/Support/support.router';

// phieu cong tac
import { phieuCongTacRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/phieuCongTac.router';
import { dieuKienTienHanhRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/DieuKienTienHanh/dieuKienTienHanh.router';
import { thuTucCatDienRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/ThuTucCatDien/thuTucCatDien.router';
import { thuTucTiepDatRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/ThuTucTiepDat/thuTucTiepDat.router';
import { thuTucRaoChanBienBaoRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/ThuTucRaoChanBienBao/thuTucRaoChanBienBao.router';
import { thuTucPhamViLamViecRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/ThuTucPhamViLamViec/thuTucPhamViLamViec.router';
import { thuTucCanhBaoChiDanRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/ThuTucCanhBaoChiDan/thuTucCanhBaoChiDan.router';
import { bienPhapAnToanBoSungRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/BienPhapAnToanBoSung/bienPhapAnToanBoSung.router';
import { danhSachNhanVienCongTacRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/DanhSachNhanVienCongTac/danhSachNhanVienCongTac.router';
import { congTacHangNgayRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/CongTacHangNgay/congTacHangNgay.router';
import { banGiaoRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/BanGiao/banGiao.router';
import { phieuCongTacNgoaiRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/PhieuCongTacNgoai/phieuCongTacNgoai.router';
import { anhCongTacRouter } from './api/resources/QuanLyVanHanh/PhieuCongTac/AnhCongTac/anhCongTac.router';
import { fileImportSampleRouter } from './api/resources/TongKe/FileImportSample/fileImportSample.router';
import { caiDatMaAppRouter } from './api/resources/CaiDatMaApp/caiDatMaApp.router';
import { reportPhieuCongTacRouter } from './api/resources/Report/ReportPhieuCongTac/reportPhieuCongTac.router';
import { eventsRouter } from './api/resources/UserTrackings/Events/events.router';
import { visitsRouter } from './api/resources/UserTrackings/Visits/visits.router';
import { configChungRouter } from './api/resources/ConfigChung/configChung.router';
import { reportXuLyTonTaiRouter } from './api/resources/Report/ReportXuLyTonTai/reportXuLyTonTai.router';
import { lyLichVanHanhRouter } from './api/resources/Report/LyLichVanHanh/lyLichVanHanh.router';
import { traoLuuCongSuatRouter } from './api/resources/QuanLyVanHanh/TraoLuuCongSuat/traoLuuCongSuat.router';
import { congSuatPmissRouter } from './api/resources/QuanLyVanHanh/TraoLuuCongSuat/CongSuatPmiss/congSuatPmiss.router';
import { reportCongSuatRouter } from './api/resources/Report/ReportTraoLuuCongSuat/reportCongSuat.router';

import { removeDuplicatesRouter } from './api/resources/RemoveDuplicates/removeDuplicates.router';
import { fileTrashRouter } from './api/resources/FileTrash/fileTrash.router';
import { reportKiemTraDinhKyRouter } from './api/resources/Report/ReportKiemTraDinhKy/reportKiemTraDinhKy.router';
import { systemRouter } from './api/resources/System/system.router';
import { proxyPmisRouter } from './api/resources/TongKe/ProxyPmis/proxyPmis.router';
import { tramBienApRouter } from './api/resources/TongKe/TramBienAp/trambienap.router';
import { nhaMayDienRouter } from './api/resources/TongKe/NhaMayDien/nhamaydien.router';
import { thongKeRouter } from './api/resources/ThongKe/thongKe.router';

// router dji
import { mediaRouter } from './api/resources/DJICloud/MediaFile/mediaFile.router';
import { waylineRouter } from './api/resources/DJICloud/Wayline/wayline.router';
import { livestreamRouter } from './api/resources/DJICloud/Manage/Livestream/livestream.router';
import { deviceRouter } from './api/resources/DJICloud/Manage/Device/device.router';

const router = express.Router();

router.use('/system', systemRouter);
router.use('/configChung', configChungRouter);
router.use('/thongtinquanly', thongTinQuanLyRouter);

router.use('/chuyende', chuyenDeRouter);
router.use('/noidungkiemtradrone', ndktDroneRouter);
router.use('/maydo', mayDoRouter);
router.use('/phanbomaydo', phanBoMayDoRouter);

router.use('/danhmucthietbi', dmThietBiRouter);
router.use('/caidathethong', caiDatHeThongRouter);
router.use('/caidatpmis', caiDatPmisRouter);
router.use('/users', userRouter);
router.use('/refreshToken', refreshTokenRouter);
router.use('/donvi', donViRouter);
router.use('/vitri', viTriRouter);
router.use('/loaivitri', loaiViTriRouter);
router.use('/nhanvien', nhanVienRouter);
router.use('/hangmuc', hangMucRouter);
router.use('/loaiduongday', loaiDuongDayRouter);
router.use('/duongdaychung', duongDayChungRouter);
router.use('/duongday', duongDayRouter);
router.use('/congtrinh', congTrinhRouter);
router.use('/giaocheo', giaoCheoRouter);
router.use('/thietbi', thietBiRouter);
router.use('/hanhchinh', hanhChinhRouter);
router.use('/tinhthanh', tinhThanhRouter);
router.use('/quanhuyen', quanHuyenRouter);
router.use('/phuongxa', phuongXaRouter);
router.use('/caidatai', caiDatAiRouter);
router.use('/caidatvanhanh', caiDatVanHanhRouter);
router.use('/loaicot', loaiCotRouter);
router.use('/diahinh', diaHinhRouter);
router.use('/loaimong', loaiMongRouter);
router.use('/loaitiepdat', loaiTiepDatRouter);
router.use('/bulong', buLongNeoMongRouter);

router.use('/loaidaydan', loaiDayDanRouter);
router.use('/loaidaycapquang', loaiDayCapQuangRouter);
router.use('/daycapquang', dayCapQuangRouter);
router.use('/loaidaychongset', loaiDayChongSetRouter);
router.use('/daychongset', dayChongSetRouter);
router.use('/cotdien', cotDienRouter);
router.use('/tiepdat', tiepDatRouter);
router.use('/vanhanh', vanHanhRouter);
router.use('/daydan', dayDanRouter);
router.use('/cachdien', cachDienRouter);
router.use('/danhmuccongviec', danhMucCongViecRouter);
router.use('/danhmuccongviecphutro', danhMucCongViecPhuTroRouter);
router.use('/bienphapantoan', bienPhapAnToanRouter);
router.use('/dieukienantoan', dieuKienAnToanRouter);
router.use('/tieuchi', tieuChiRouter);
router.use('/noidungkiemtra', noiDungRouter);
router.use('/dodientro', doDienTroRouter);
router.use('/loaicachdien', loaiCachDienRouter);
router.use('/chongrung', chongRungRouter);
router.use('/role', roleRouter);
router.use('/bando', banDoRouter);

router.use('/phieugiaoviec', phieuGiaoViecRouter);
router.use('/phieumau', phieuMauRouter);
router.use('/nguoicongtac', nguoiCongTacRouter);

router.use('/khungdinhvi', khungDinhViRouter);
router.use('/tabu', taBuRouter);
router.use('/ketquakiemtra', ketQuaKiemTraRouter);
router.use('/ketquaxuly', ketQuaSuaChuaKhongKeHoachRouter);
router.use('/anhvitri', anhViTriRouter);
router.use('/file', fileRouter);
router.use('/thietbiphathien', thietBiPhatHienRouter);
router.use('/batthuongphathien', batThuongPhatHienRouter);
router.use('/trangthaiphieugiaoviec', trangThaiPhieuGiaoViec);
router.use('/phieudonhietdo', ketQuaDoNhietDoRouter);

router.use('/hoso', hoSoRouter);
router.use('/tailieu', taiLieuRouter);
router.use('/linkfile', linkFileRouter);
router.use('/loaicongviec', loaiCongViecRouter);

router.use('/danhmucdrone', danhMucDroneRouter);
router.use('/drone', droneRouter);
router.use('/phukiendrone', phuKienRouter);
router.use('/notification', notificationRouter);
router.use('/remind', remindRouter);
router.use('/report', reportRouter);
router.use('/baocaonangsuat', reportNSLVRouter);
router.use('/dashboard', dashboardRouter);
router.use('/phieukiemtra', phieuKiemTraRouter);
router.use('/phieusuachua', phieuSuaChuaRouter);
router.use('/phieudoluong', phieuDoLuongRouter);
router.use('/phieucongtacphutro', phieuCongTacPhuTroRouter);
router.use('/ketquadonhietdo', ketQuaDoNhietDoRouter);
router.use('/ketquadocorocam', ketQuaDoCorocamRouter);
router.use('/ketquadonhietdocomposite', ketQuaDoNhietDoCompositeRouter);
router.use('/ketquadocuongdodientruong', ketQuaDoCuongDoDienTruongRouter);
router.use('/ketquadokhoangcachgiaocheo', ketQuaDoKhoangCachGiaoCheoRouter);
router.use('/congviecphutro', congViecPhuTroRouter);
router.use('/tinhhinhtontai', tinhHinhTonTaiRouter);
router.use('/hieuchinhtontai', hieuChinhTonTaiRouter);

router.use('/phieucongtac', phieuCongTacRouter);
router.use('/dieukientienhanh', dieuKienTienHanhRouter);
router.use('/thutuccatdien', thuTucCatDienRouter);
router.use('/thutuctiepdat', thuTucTiepDatRouter);
router.use('/thutucraochanbienbao', thuTucRaoChanBienBaoRouter);
router.use('/thutucphamvilamviec', thuTucPhamViLamViecRouter);
router.use('/thutuccanhbaochidan', thuTucCanhBaoChiDanRouter);
router.use('/bienphapantoanbosung', bienPhapAnToanBoSungRouter);
router.use('/danhsachnhanviencongtac', danhSachNhanVienCongTacRouter);
router.use('/congtachangngay', congTacHangNgayRouter);
router.use('/bangiao', banGiaoRouter);
router.use('/phieucongtacngoai', phieuCongTacNgoaiRouter);
router.use('/anhcongtac', anhCongTacRouter);

router.use('/tontai', tonTaiRouter);
router.use('/ketquasuachuacokehoach', ketQuaSuaChuaCoKeHoachRouter);
router.use('/dokhoangcachphadat', ketQuaDoKhoangCachPhaDatRouter);

router.use('/khoangcot', khoangCotRouter);
router.use('/khoangneo', khoangNeoRouter);

router.use('/hanhlangtuyen', hanhLangTuyenRouter);
router.use('/tongkemoinoi', tongKeMoiNoiRouter);
router.use('/congviecphatsinh', congViecPhatSinhRouter);
router.use('/truongdulieu', truongDuLieuRouter);

router.use('/nhomthietbidungcu', nhomThietBiDungCuRouter);
router.use('/thietbidungcu', thietBiDungCuRouter);
router.use('/sotheodoithietbi', soTheoDoiThietBiRouter);
router.use('/sucoduongday', suCoDuongDayRouter);
router.use('/tontaicaptren', tonTaiCapTrenRouter);

router.use('/taptin', tapTinRouter);
router.use('/vitricongviec', viTriCongViecRouter);
router.use('/cauhinhbay', cauHinhBayRouter);
router.use('/missionpoint', missionPointRouter);
router.use('/hosothietbi', hoSoThietBiRouter);

router.use('/tontaithoidiembangiao', tonTaiThoiDiemBanGiaoRouter);
router.use('/congtrinhxaydung', tonTaiCongTrinhXayDungRouter);
router.use('/khiemkhuyetthietbi', khiemKhuyetThietBiRouter);
router.use('/baocaokhiemkhuyetthietbi', reportKKTBRouter);

router.use('/filebienban', fileBienBanRouter);
router.use('/anhtontai', anhTonTaiRouter);
router.use('/tontailamquang', tonTaiLamQuangRouter);
router.use('/bienbanxacnhan', bienBanRouter);
router.use('/chitietbienban', chiTietBienBanRouter);

router.use('/tieuchilamquang', tieuChiLamQuangRouter);
router.use('/noidunglamquang', noiDungLamQuangRouter);

router.use('/phuluctontai', phuLucTonTaiRouter);
router.use('/importcongtrinh', importCongTrinhRouter);
router.use('/importduongday', importDuongDayRouter);
router.use('/importphieugiaoviec', importPhieuGiaoViecRouter);
router.use('/filesample', fileImportSampleRouter);
router.use('/cai-dat-app', caiDatMaAppRouter);
router.use('/nghiemthuhanhlangtuyen', ntHanhLangTuyenRouter);

router.use('/reportdophadat', reportDoPhaDatRouter);
router.use('/reportdodientro', reportDoDienTroRouter);
router.use('/reportdonhietdo', reportDoNhietDoRouter);
router.use('/reportphieugiaoviec', reportPhieuGiaoViecRouter);
router.use('/reportphieucongtac', reportPhieuCongTacRouter);
router.use('/reportxulytontai', reportXuLyTonTaiRouter);
router.use('/dongbodothongso', dongBoRouter);
router.use('/syncpmis', syncPmisRouter);
router.use('/imagelog', imageLogRouter);
router.use('/serverlogs', serverLogRouter);
router.use('/log-gop-duong-day', logGopDuongDayRouter);

router.use('/lichsudonhietdo', lichSuDoNhietDoRouter);
router.use('/lylichvanhanh', lyLichVanHanhRouter);

router.use('/gpsuser', gpsUserRouter);
/*Start User Tracking System*/
router.use('/events', eventsRouter);
router.use('/visits', visitsRouter);
/*End User Tracking System*/
router.use('/thongke', thongKeRouter);
router.use('/traoluucongsuat', traoLuuCongSuatRouter);
router.use('/congsuatpmiss', congSuatPmissRouter);
router.use('/reporttraoluucongsuat', reportCongSuatRouter);
router.use('/remove-duplicates', removeDuplicatesRouter);
router.use('/filetrash', fileTrashRouter);
router.use('/support', supportRouter);

router.use('/reportkiemtradinhky', reportKiemTraDinhKyRouter);
router.use('/proxy-pmis', proxyPmisRouter);
router.use('/trambienap', tramBienApRouter);
router.use('/nhamaydien', nhaMayDienRouter);
router.use('/rasoatdulieu', raSoatDuLieuRouter);

router.use('/dji/media', mediaRouter);
router.use('/dji/wayline', waylineRouter);
router.use('/dji/live', livestreamRouter);
router.use('/dji/device', deviceRouter)

module.exports = router;
