import { Router } from 'express';
import passport from 'passport';
import * as ServerLogsController from './serverLog.controller';
import { authorizationMiddleware, sysadminAuthorizationMiddleware } from '../RBAC/middleware';
import HoSoPermission from '../RBAC/permissions/HoSoPermission';
import { taiLieuRouter } from '../TaiLieu/taiLieu.router';

export const serverLogRouter = Router();
serverLogRouter.use(passport.authenticate('jwt', { session: false }));
taiLieuRouter.get('*', sysadminAuthorizationMiddleware());

serverLogRouter.route('/')
  .get(ServerLogsController.getAll);

serverLogRouter.route('/downloadlog')
  .get(ServerLogsController.downloadFileLog);

