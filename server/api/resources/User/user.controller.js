import path from 'path';
import * as admin from 'firebase-admin';
import moment from 'moment';
import { t } from 'i18next';

import jwt from '../../helpers/jwt';
import * as responseHelper from '../../helpers/responseHelper';
import queryHelper from '../../helpers/queryHelper';
import { generateChangePasswordEmail } from '../../helpers/emailHelper';

import userService from './user.service';
import User from './user.model';

import * as fileUtils from '../../utils/fileUtils';
import { sendEmail } from '../../utils/mailHelper';
import { createFolderIfNotExist, deleteFile, getFilePath } from '../../utils/fileUtils';
import * as ImageUtils from '../../utils/ImageUtilsGM';
import * as Ldap3 from '../../utils/ldap3';
import { findUserLDAP, ldapAuthenticateAPI } from '../../utils/ldap3';
import { extractIds } from '../../utils/dataconverter';

import { STORE_DIRS, USER_NAME_ADDON } from '../../constant/constant';
import { LOAI_TAI_KHOAN } from './LoaiTaiKhoan';
import { LOAI_NGUOI_DUNG } from './LoaiNguoiDung';
import { TRANG_THAI_PHIEU } from '../DanhMuc/TrangThaiCongViec';

import ROLE from '../Role/role.model';
import Setting, { FORCE_ALL_2FA } from '../CaiDatHeThong/caiDatHeThong.model';

import { getConfig } from '../../../config/config';
import * as DonViService from '../DonVi/donVi.service';
import * as RoleService from '../Role/role.service';
import * as RefreshTokenService from '../RefreshToken/refreshToken.service';
import * as NguoiCongTacService from '../QuanLyVanHanh/NguoiCongTac/nguoiCongTac.service';
import * as PhieuGiaoViecService from '../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';

import * as permission from '../RBAC/permissionHelper';
import resources from '../RBAC/Resources';
import actions from '../RBAC/Actions';
import CommonError from '../../error/CommonError';
import { loggerError, loggerResponse } from '../../logs/middleware';
import { handleRemoveDuplicates } from './user.duplicates';
import cookie from 'cookie';

const config = getConfig(process.env.NODE_ENV);
const sortOpts = { name: 1, full_name: 1, username: 1 };

admin.initializeApp({
  credential: admin.credential.cert(config.firebase_config),
});

async function getAllUsers(req, includeDeletedUnit = false) {
  const userReqPermissions = await getPermissionByUserId(req.user._id);
  let query = queryHelper.extractQueryParam(req, ['username', 'full_name', 'email', 'phone']);
  const criteria = query.criteria;
  criteria.is_system_admin = false;
  if ((!req.query.include_children || req.query.include_children === 'false') && req.query.don_vi_id) {
    criteria.don_vi_id = req.query.don_vi_id;
  } else {
    if (includeDeletedUnit) {
      criteria.don_vi_id = await DonViService.getDonViQueryIncludeDeleted(req, criteria.don_vi_id);
    } else {
      criteria.don_vi_id = await DonViService.getDonViQuery(req, criteria.don_vi_id);
    }
  }
  const options = query.options;
  options.select = '-password -is_deleted';
  options.populate = [
    { path: 'don_vi_id' },
    { path: 'role_id' },
  ];
  if (!options.sort) {
    options.sort = sortOpts;
  }
  const users = await User.paginate(criteria, options);
  users.docs.forEach(doc => {
    if (req.user.is_system_admin) {
      doc.allow_update = true;
    } else {
      let permissionsUpdate = Array.isArray(doc.permissions) ? doc.permissions : [];
      if (Array.isArray(doc.role_id)) {
        doc.role_id.forEach(role => {
          if (Array.isArray(role.permissions)) {
            permissionsUpdate = [...permissionsUpdate, ...role.permissions];
          }
        });
        doc.allow_update = !permissionsUpdate.filter(n => !userReqPermissions.includes(n)).length;
        if (!doc.allow_update) {
          doc.alias = '**********';
        }
      }
    }
  });
  return users;
}

export default {
  async getTrongCongtac(req, res) {
    try {
      const tuNgay = req.query.tu_ngay;
      const denNgay = req.query.den_ngay;
      const phieuGiaoViecId = req.query.phieu_giao_viec_id;

      delete req.query.tu_ngay;
      delete req.query.den_ngay;
      delete req.query.phieu_giao_viec_id;

      if (!tuNgay || !denNgay) {
        loggerError(res.req, 'Yêu cầu thời gian bắt đầu và thời gian kết thúc công việc');
        return res.status(400).json({
          success: false,
          message: 'Yêu cầu thời gian bắt đầu và thời gian kết thúc công việc',
        });
      }

      const query = {
        _id: { $ne: phieuGiaoViecId },
        // $or: [
        //   { thoi_gian_cong_tac_bat_dau: { $gte: tuNgay, $lte: denNgay } },
        //   { thoi_gian_cong_tac_ket_thuc: { $gte: tuNgay, $lte: denNgay } },
        //   {
        //     thoi_gian_cong_tac_bat_dau: { $lte: tuNgay },
        //     thoi_gian_cong_tac_ket_thuc: { $gte: denNgay },
        //   },
        // ],
        $or: [
          { thoi_gian_cong_tac_bat_dau: { $lte: denNgay }, thoi_gian_cong_tac_ket_thuc: { $gte: denNgay } },
          { thoi_gian_cong_tac_bat_dau: { $lte: tuNgay }, thoi_gian_cong_tac_ket_thuc: { $gte: tuNgay } },
          { thoi_gian_cong_tac_bat_dau: { $gte: tuNgay }, thoi_gian_cong_tac_ket_thuc: { $lte: denNgay } },
        ],
        trang_thai_cong_viec: {
          $nin: [TRANG_THAI_PHIEU.HUY_PHIEU.code, TRANG_THAI_PHIEU.XAC_NHAN_KHOA.code],
        },
        is_deleted: false,
      };
      const allPhieuGiaoViec = await PhieuGiaoViecService.getAllPopulate(query);

      const phieuGiaoViecIds = extractIds(allPhieuGiaoViec);
      const allNguoiCongTac = await NguoiCongTacService
        .getAll({ phieu_giao_viec_id: phieuGiaoViecIds, is_deleted: false })
        .populate({ path: 'user_id', select: 'full_name username phone bac_an_toan' });

      let objNguoiCongTac = {}, skipNguoiCongTac = [];
      allNguoiCongTac.forEach(nguoiCongTac => {
        if (nguoiCongTac.user_id) {
          // if (nguoiCongTac.phieu_giao_viec_id.toString() === phieuGiaoViecId) {
          //   skipNguoiCongTac.push(nguoiCongTac.user_id);
          // } else {
          objNguoiCongTac[nguoiCongTac.user_id._id] = nguoiCongTac.user_id;
          // }
        }
      });
      allPhieuGiaoViec.forEach(phieuGiaoViec => {
        if (phieuGiaoViec.chi_huy_truc_tiep_id) {
          // if (phieuGiaoViec._id.toString() === phieuGiaoViecId) {
          //   skipNguoiCongTac.push(phieuGiaoViec.chi_huy_truc_tiep_id);
          // } else {
          objNguoiCongTac[phieuGiaoViec.chi_huy_truc_tiep_id._id] = phieuGiaoViec.chi_huy_truc_tiep_id;
          // }
        }
      });
      // skipNguoiCongTac.forEach((nguoiCongTac) => {
      //   delete objNguoiCongTac[nguoiCongTac._id];
      // });

      const users = await getAllUsers(req);

      users.docs.forEach(user => {
        if (objNguoiCongTac[user._id]) {
          user.trong_cong_tac = true;
        }
      });
      return responseHelper.success(res, users);
    } catch (err) {
      console.error(err);
      loggerError(res.req, err);
      return res.status(500).send(err);
    }
  },

  async checkUserLDAP(req, res) {
    const { t } = req;
    try {
      const { criteria } = queryHelper.extractQueryParam(req);
      delete criteria.is_deleted;
      if (!criteria.username) {
        return responseHelper.error(res, { message: `${t(LOAI_TAI_KHOAN.TAI_KHOAN_HRMS.code)} ${t('user_can_not_be_blank')}` }, 404);
      }
      criteria.username = criteria.username.toString().toLowerCase();
      let checkUsernameValid = await findUserLDAP(criteria.username);
      if (!checkUsernameValid.success) {
        return responseHelper.error(res, { message: t('error_user_no_account_exist') }, 404);
      }

      const checkExist = await User.findOne({ ...criteria, username: USER_NAME_ADDON + criteria.username }, { _id: 1 });
      if (checkExist) {
        return responseHelper.error(res, { message: `${t('user_account')} ${criteria.username} ${t('user_exist')}` }, 400);
      }

      return responseHelper.success(res, checkUsernameValid.user);
    } catch (err) {
      console.error(err);
      loggerError(res.req, err);
      return res.status(500).send(err);
    }
  },
  async signupFree(req, res) {
    try {
      const { t } = req;
      const { value, error } = userService.validateSignup(req.body, 'POST');
      value.permissions = [];
      value.role = null;
      value.don_vi_id = null;
      value.is_system_admin = false;
      value.loai_nguoi_dung = LOAI_NGUOI_DUNG.NGUOI_TU_DANG_KY.value;

      if (error) {
        console.log(error);
        loggerError(res.req, error);
        return res.status(400).json(error.details);
      }

      const checkUsername = await User.findOne({ username: value.username });
      if (checkUsername) {
        loggerError(res.req, t('error_user_account_is_registered'));
        return res.status(400).json({ success: false, message: t('error_user_account_is_registered') });
      }

      if (value.email) {
        const checkEmail = await User.findOne({ email: value.email });
        if (checkEmail) {
          loggerError(res.req, t('error_user_email_was_registered'));
          return res.status(400).json({ success: false, message: t('error_user_email_was_registered') });
        }
      }

      if (value.phone) {
        const checkPhone = await User.findOne({ phone: value.phone });
        if (checkPhone) {
          loggerError(res.req, t('error_user_phone_was_registered'));
          return res.status(400).json({ success: false, message: t('error_user_phone_was_registered') });
        }
      }

      if (value.alias) {
        const checkAlias = await User.findOne({ alias: value.alias });
        if (checkAlias) {
          loggerError(res.req, t('error_user_alias_already_exists'));
          return res.status(400).json({ success: false, message: t('error_user_alias_already_exists') });
        }
      }

      const user = await User.create(value);

      if (value.email) {
        let mailOptions = {
          from: `${t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
          to: value.email, // list of receivers
          subject: t('email_user_create_subject'), // Subject line
          //text: 'Pass moi la 123455', // plaintext body
          html: `<h2>${t('email_user_create_html1')}</h2>
              <div><strong>${t('email_user_create_html2')}</strong>${value.full_name}</div>
              <div><strong>${t('email_user_create_html3')}</strong>${value.username}</div>    
              <div><strong>${t('email_user_create_html4')}</strong>${value.phone}</div>
              <div><strong>${t('email_user_create_html5')}</strong>${value.email}</div>
              <div>${t('email_user_create_html6')}<a href="${config.host_admin}">Link</a></div>`, // html body
        };

        sendEmail(mailOptions, (err) => {
          if (err) {
            console.log(err);
          }
        });
      }
      return responseHelper.success(res, user);
    } catch (err) {
      console.error(err);
      return responseHelper.error(res, err);
    }
  },

  async signup(req, res) {
    try {
      const { t } = req;
      const { value, error } = userService.validateSignup(JSON.parse(req.body.json_data), 'POST');
      if (error) {
        console.log(error);
        return res.status(400).json(error.details);
      }
      value.username = value.username.toLowerCase();
      const checkUsername = await User.findOne({
        username: value.username,
        loai_nguoi_dung: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.value,
      });
      if (checkUsername) {
        return res.status(400).json({ success: false, message: t('error_user_account_is_registered') });
      }

      if (!value.email) {
        return res.status(400).json({ success: false, message: t('error_user_email_is_required') });
      }

      const checkEmail = await User.findOne({
        email: value.email,
        loai_nguoi_dung: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.value,
      });

      if (checkEmail) {
        return res.status(400).json({ success: false, message: t('error_user_email_was_registered') });
      }

      if (value.phone) {
        const checkPhone = await User.findOne({
          phone: value.phone,
          loai_nguoi_dung: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.value,
        });
        if (checkPhone) {
          return res.status(400).json({ success: false, message: t('error_user_phone_was_registered') });
        }
      }

      if (value.alias) {
        const checkAlias = await User.findOne({
          alias: value.alias,
          loai_nguoi_dung: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.value,
        });
        if (checkAlias) {
          return res.status(400).json({ success: false, message: t('error_user_alias_already_exists') });
        }
      }

      if (!req.user.is_system_admin) {
        if (await handleCheckOverPermission(req.user._id, value)) {
          return responseHelper.error(res, { message: t('error_user_invalid_permission') });
        }
      }

      // update avatar
      if (req.files?.avatar?.path) {
        const avatar_folder = path.join(STORE_DIRS.AVATAR);
        createFolderIfNotExist(avatar_folder);
        const avatar_id = fileUtils.createUniqueFileName(req.files.avatar.originalFilename);
        const avatarPath = await ImageUtils.rotate(avatar_id, req.files.avatar.path);
        await fileUtils.createByName(avatarPath, avatar_id, avatar_folder);
        value.avatar = avatar_id;
      }
      // update chu_ky
      if (req.files?.chu_ky?.path) {
        const chu_ky_folder = path.join(STORE_DIRS.CHU_KY);
        createFolderIfNotExist(chu_ky_folder);
        const chu_ky_id = fileUtils.createUniqueFileName(req.files.chu_ky.originalFilename);
        const chu_ky_Path = await ImageUtils.rotate(chu_ky_id, req.files.chu_ky.path);
        await fileUtils.createByName(chu_ky_Path, chu_ky_id, chu_ky_folder);
        value.chu_ky_id = chu_ky_id;
      }

      const user = await User.create(value);

      if (value.email) {
        let mailOptions = {
          from: `${t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
          to: value.email, // list of receivers
          subject: t('email_user_create_subject'), // Subject line
          //text: 'Pass moi la 123455', // plaintext body
          html: `<h2>${t('email_user_create_html1')}</h2>
              <div><strong>${t('email_user_create_html2')}</strong>${value.full_name}</div>
              <div><strong>${t('email_user_create_html3')}</strong>${value.username}</div>
              <div><strong>${t('email_user_create_html4')}</strong>${value.phone}</div>
              <div><strong>${t('email_user_create_html5')}</strong>${value.email}</div>
              <div>${t('email_user_create_html6')}<a href="${config.host_admin}">Link</a></div>`, // html body
        };

        sendEmail(mailOptions, (err) => {
          if (err) {
            console.log(err);
          }
        });
      }
      return responseHelper.success(res, user);
    } catch (err) {
      console.error(err);
      loggerError(res.req, err);
      return res.status(500).send(err);
    }
  },
  async login(req, res) {
    try {

      delete req.body.loai_tai_khoan;
      const { t } = req;
      const { value, error } = userService.validateLogin(req.body);
      const originUserName = value.username;
      value.username = value.username?.toLowerCase();
      if (error) {
        return res.status(400).json(error);
      }
      const caiDatHeThong = await Setting.findOne();
      let responseCapcha = {};
      if (caiDatHeThong.activate_wrong_pass_to_capcha) {
        responseCapcha = {
          wrong_pass_to_capcha: caiDatHeThong.wrong_pass_to_capcha,
          activate_wrong_pass_to_capcha: caiDatHeThong.activate_wrong_pass_to_capcha,
        };
      }
      let user = await User.findOne({
        username: { $in: [value.username, originUserName] },
        is_deleted: false,
      }).lean();
      if (!user) {
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(400).json({
          success: false,
          ...responseCapcha,
          message: t('error_user_login_username_password_wrong'),
        });
      }

      if (!user.active) {
        loggerError(res.req, t('error_user_login_account_locked'));
        return res.status(400).json({
          success: false,
          is_deactivate: true,
          ...responseCapcha,
          message: t('error_user_login_account_locked'),
        });
      }

      let authenticated;
      if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HRMS.code) {
        const sAMAccountName = user.username?.replace(USER_NAME_ADDON, '');
        const ldapData = await findUserLDAP(sAMAccountName);
        if (ldapData?.user?.dn) {
          authenticated = await ldapAuthenticateAPI(ldapData.user.dn, value.password);
        }
      } else if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code && user?.password) {
        authenticated = userService.comparePassword(value.password, user.password);
      }

      if (!authenticated) {

        if (!caiDatHeThong.activate_times_wrong_pass) {
          await User.findOneAndUpdate({ _id: user._id },
            { times_wrong_pass: 0 }).lean();
        }

        let msgString = t('error_user_login_username_password_wrong');
        let dataUpdate;
        if (caiDatHeThong.activate_times_wrong_pass) {
          user.times_wrong_pass = !user.times_wrong_pass ? 1 : user.times_wrong_pass + 1;
          dataUpdate = { times_wrong_pass: user.times_wrong_pass };

          msgString = `${t('error_user_password_wrong')} ${t('enterd_wrong')} ${user.times_wrong_pass} ${t('times')} ${t('you_have')} ${caiDatHeThong.times_wrong_pass - user.times_wrong_pass} ${t('attempts_left')}`;
          if (user.times_wrong_pass === caiDatHeThong.times_wrong_pass) {
            dataUpdate.active = false;
            msgString = t('error_user_login_account_locked');
          }
          let dataRes = { success: false, times_wrong_pass: user.times_wrong_pass, message: msgString };
          if (caiDatHeThong.activate_wrong_pass_to_capcha) {
            dataRes = { ...dataRes, ...responseCapcha };
          }
          await User.findOneAndUpdate({ _id: user._id }, dataUpdate);
          loggerError(res.req, t('error_user_login_username_password_wrong'));
          return res.status(400).json(dataRes);
        }
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(400).json({ success: false, ...responseCapcha, message: msgString });
      }

      user = await User.findOneAndUpdate({ _id: user._id },
        { times_wrong_pass: 0 });

      const setting = await Setting.findOne().lean();
      const isLogin2FA = !(!setting.login_tfa || (setting.force_all_login_tfa === FORCE_ALL_2FA.DESIGNATED && !user.login_tfa));
      if (isLogin2FA) {
        const phone = user.phone;
        const token2fa = jwt.issue({ id: user._id, isUser: true, phone: phone }, '5m');
        loggerResponse(res.req);
        return res.json({ token2fa, phone });
      }
      await getLoginResponse(req, res, user);
    } catch (err) {
      return responseHelper.error(res, err);
    }
  },

  async loginUsingLDAP(req, res) {
    try {

      delete req.body.loai_tai_khoan;
      const { t } = req;
      const { value, error } = userService.validateLogin(req.body);
      const originUserName = value.username;
      value.username = value.username?.toLowerCase();
      if (error) {
        return res.status(400).json(error);
      }
      const caiDatHeThong = await Setting.findOne();
      let responseCapcha = {};
      if (caiDatHeThong.activate_wrong_pass_to_capcha) {
        responseCapcha = {
          wrong_pass_to_capcha: caiDatHeThong.wrong_pass_to_capcha,
          activate_wrong_pass_to_capcha: caiDatHeThong.activate_wrong_pass_to_capcha,
        };
      }
      let user = await User.findOne({
        username: { $in: [value.username, originUserName] },
        is_deleted: false,
      }).lean();
      if (!user) {
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(400).json({
          success: false,
          ...responseCapcha,
          message: t('error_user_login_username_password_wrong'),
        });
      }

      if (!user.active) {
        loggerError(res.req, t('error_user_login_account_locked'));
        return res.status(400).json({
          success: false,
          is_deactivate: true,
          ...responseCapcha,
          message: t('error_user_login_account_locked'),
        });
      }

      let authenticated = { success: false, user };
      if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HRMS.code) {
        const sAMAccountName = user.username?.replace(USER_NAME_ADDON, '');
        const ldapData = await Ldap3.findUserLDAP(sAMAccountName);
        if (ldapData?.user?.dn) {
          authenticated = await Ldap3.ldapAuthenticateAPI(ldapData.user.dn, value.password);
        }
      } else if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code && user?.password) {
        authenticated.success = userService.comparePassword(value.password, user.password);
      }

      if (!authenticated.success) {
        if (!caiDatHeThong.activate_times_wrong_pass) {
          await User.findOneAndUpdate({ _id: user._id }, { times_wrong_pass: 0 });
        }

        let msgString = t('error_user_login_username_password_wrong');
        let dataUpdate;
        if (caiDatHeThong.activate_times_wrong_pass) {
          user.times_wrong_pass = !user.times_wrong_pass ? 1 : user.times_wrong_pass + 1;
          dataUpdate = { times_wrong_pass: user.times_wrong_pass };

          msgString = `${t('error_user_password_wrong')} ${t('enterd_wrong')} ${user.times_wrong_pass} ${t('times')} ${t('you_have')} ${caiDatHeThong.times_wrong_pass - user.times_wrong_pass} ${t('attempts_left')}`;
          if (user.times_wrong_pass === caiDatHeThong.times_wrong_pass) {
            dataUpdate.active = false;
            msgString = t('error_user_login_account_locked');
          }
          let dataRes = {
            success: false,
            times_wrong_pass: user.times_wrong_pass,
            message: msgString,
            // authenticated,
          };
          if (caiDatHeThong.activate_wrong_pass_to_capcha) {
            dataRes = { ...dataRes, ...responseCapcha };
          }
          await User.findOneAndUpdate({ _id: user._id }, dataUpdate);
          loggerError(res.req, t('error_user_login_username_password_wrong'));
          return res.status(400).json(dataRes);
        }
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(400).json({
          success: false, ...responseCapcha,
          message: msgString,
          // authenticated,
        });
      }

      user = await User.findOneAndUpdate(
        { _id: user._id },
        { times_wrong_pass: 0 },
        { new: true },
      ).lean();

      const setting = await Setting.findOne().lean();
      const isLogin2FA = !(!setting.login_tfa || (setting.force_all_login_tfa === FORCE_ALL_2FA.DESIGNATED && !user.login_tfa));
      if (isLogin2FA) {
        const phone = user.phone;
        const token2fa = jwt.issue({ id: user._id, isUser: true, phone: phone }, '5m');
        loggerResponse(res.req);
        return res.json({ token2fa, phone });
      }
      await getLoginResponse(req, res, user);
    } catch (err) {
      return responseHelper.error(res, err);
    }
  },

  async logout(req, res) {
    try {
      const refreshToken = extractRefreshToken(req);
      const query = { refresh_token: refreshToken, is_deleted: false };
      const dataResponse = await RefreshTokenService.deleteOne(query);

      return responseHelper.success(res, { success: dataResponse?.deletedCount });
    } catch (err) {
      return responseHelper.error(res, err);
    }
  },

  async loginDrone(req, res) {
    try {

      delete req.body.loai_tai_khoan;
      const { t } = req;
      const { value, error } = userService.validateLogin(req.body);
      const originUserName = value.username;
      value.username = value.username?.toLowerCase();
      if (error) {
        return res.status(400).json(error);
      }
      const caiDatHeThong = await Setting.findOne();
      let responseCapcha = {};
      if (caiDatHeThong.activate_wrong_pass_to_capcha) {
        responseCapcha = {
          wrong_pass_to_capcha: caiDatHeThong.wrong_pass_to_capcha,
          activate_wrong_pass_to_capcha: caiDatHeThong.activate_wrong_pass_to_capcha,
        };
      }
      let user = await User.findOne({
        username: { $in: [value.username, originUserName] },
        is_deleted: false,
      }).lean();
      if (!user) {
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(401).json({
          success: false,
          ...responseCapcha,
          message: t('error_user_login_username_password_wrong'),
        });
      }

      if (!user.active) {
        loggerError(res.req, t('error_user_login_account_locked'));
        return res.status(401).json({
          success: false,
          is_deactivate: true,
          ...responseCapcha,
          message: t('error_user_login_account_locked'),
        });
      }

      let authenticated = { success: false, user };
      if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HRMS.code) {
        const sAMAccountName = user.username?.replace(USER_NAME_ADDON, '');
        const ldapData = await Ldap3.findUserLDAP(sAMAccountName);
        if (ldapData?.user?.dn) {
          authenticated = await Ldap3.ldapAuthenticateAPI(ldapData.user.dn, value.password);
        }
      } else if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code && user?.password) {
        authenticated.success = userService.comparePassword(value.password, user.password);
      }

      if (!authenticated.success) {
        if (!caiDatHeThong.activate_times_wrong_pass) {
          await User.findOneAndUpdate({ _id: user._id }, { times_wrong_pass: 0 });
        }

        let msgString = t('error_user_login_username_password_wrong');
        let dataUpdate;
        if (caiDatHeThong.activate_times_wrong_pass) {
          user.times_wrong_pass = !user.times_wrong_pass ? 1 : user.times_wrong_pass + 1;
          dataUpdate = { times_wrong_pass: user.times_wrong_pass };

          msgString = `${t('error_user_password_wrong')} ${t('enterd_wrong')} ${user.times_wrong_pass} ${t('times')} ${t('you_have')} ${caiDatHeThong.times_wrong_pass - user.times_wrong_pass} ${t('attempts_left')}`;
          if (user.times_wrong_pass === caiDatHeThong.times_wrong_pass) {
            dataUpdate.active = false;
            msgString = t('error_user_login_account_locked');
          }
          let dataRes = {
            success: false,
            times_wrong_pass: user.times_wrong_pass,
            message: msgString,
            // authenticated,
          };
          if (caiDatHeThong.activate_wrong_pass_to_capcha) {
            dataRes = { ...dataRes, ...responseCapcha };
          }
          await User.findOneAndUpdate({ _id: user._id }, dataUpdate);
          loggerError(res.req, t('error_user_login_username_password_wrong'));
          return res.status(401).json(dataRes);
        }
        loggerError(res.req, t('error_user_login_username_password_wrong'));
        return res.status(401).json({
          success: false, ...responseCapcha,
          message: msgString,
          // authenticated,
        });
      }

      user = await User.findOneAndUpdate(
        { _id: user._id },
        { times_wrong_pass: 0 },
        { new: true },
      ).lean();

      const setting = await Setting.findOne().lean();
      const isLogin2FA = !(!setting.login_tfa || (setting.force_all_login_tfa === FORCE_ALL_2FA.DESIGNATED && !user.login_tfa));
      if (isLogin2FA) {
        const phone = user.phone;
        const token2fa = jwt.issue({ id: user._id, isUser: true, phone: phone }, '5m');
        loggerResponse(res.req);
        return res.json({ token2fa, phone });
      }
      await getLoginDroneResponse(req, res, user);
    } catch (err) {
      console.error(err);
      return res.status(401).send(err);
    }
  },

  async login2FAStepTwo(req, res) {
    try {
      const { token2fa, token_firebase } = req.body;
      let dataUserFirebase = await admin.auth().verifyIdToken(token_firebase);
      const payload = await jwt.verifyToken(token2fa);
      const phoneInFireBase = dataUserFirebase.phone_number.slice(-9);
      const phoneInDatabase = payload.phone.slice(-9);
      if (phoneInFireBase === phoneInDatabase) {
        const userStep1 = await User.findById(payload.id).lean();
        return await getLoginResponse(req, res, userStep1);
      }
      return res.status(401).json({
        success: false,
        message: `Số điện thoại không hợp lệ`,
      });

    } catch (err) {
      console.error('err', err);
      return responseHelper.error(res, { message: 'Lỗi xác thực OTP. Vui lòng thử lại' });
    }
  },
  async registerDevice(req, res) {
    try {
      const user = req.user;
      if (user) {
        return responseHelper.error(res, 404, '');
      }

      await userService.addOrUpdateDeviceToken(user, req.body.deviceToken);

      responseHelper.success(res, req.body);
    } catch (err) {
      console.error(err);
      return responseHelper.error(res, err);
    }
  },
  async unregisterDevice(req, res) {
    try {
      await userService.findAndRemoveDeviceToken(req.body.deviceToken);
      // Remove old refresh token info to DB
      if (req.body?.refreshToken) await RefreshTokenService.deleteMany({ refresh_token: req.body.refreshToken });
      return responseHelper.success(res, req.body);
    } catch (err) {
      console.error(err);
      return responseHelper.error(res, err);
    }
  },

  async authenticate(req, res) {
    let userInfo = await req.user
      .populate('role_id')
      .populate('don_vi_id')
      .execPopulate();
    loggerResponse(res.req);
    return res.status(200).json(userInfo);
  },

  async findAll(req, res) {
    try {
      const users = await getAllUsers(req);
      return responseHelper.success(res, users);
    } catch (err) {
      console.error(err);
      loggerError(res.req, err);
      return res.status(500).send(err);
    }
  },

  async findAllIncludeDeletedUnit(req, res) {
    try {
      const users = await getAllUsers(req, true);
      return responseHelper.success(res, users);
    } catch (err) {
      console.error(err);
      return res.status(500).send(err);
    }
  },

  async getAllForSysadmin(req, res) {
    try {
      if (!req.user.is_system_admin) {
        return responseHelper.error(res, { message: t('have_unexpected_error') }, 404);
      }

      const query = queryHelper.extractQueryParam(req, ['username', 'full_name', 'email', 'phone']);

      const criteria = query.criteria;
      const options = query.options;
      options.select = '-password -is_system_admin';
      options.populate = [
        { path: 'don_vi_id' },
        { path: 'role_id' },
      ];
      if (!options.sort) {
        options.sort = sortOpts;
      }
      const users = await User.paginate(criteria, options);
      return responseHelper.success(res, users);
    } catch (err) {
      console.error(err);
      return responseHelper.error(res, err, 500);
    }
  },

  async findOne(req, res) {
    try {
      const { id } = req.params;
      const user = await User.findById(id);
      if (!user) {
        return responseHelper.error(res, null, 404);
      }
      return res.json(user);
    } catch (err) {
      console.error(err);
      return res.status(500).send(err);
    }
  },
  async delete(req, res) {
    try {
      const { id } = req.params;

      if (!req.user.is_system_admin) {
        const currentUser = await userService.getOne({ _id: id });
        if (!currentUser || await handleCheckOverPermission(req.user._id, currentUser)) {
          return responseHelper.error(res, { message: t('user_delete_insufficient_permissions') });
        }
      }
      const user = await User.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
      if (!user) {
        return responseHelper.error(res, '', 404);
      }
      return responseHelper.success(res, user);
    } catch (err) {
      console.error(err);
      return res.status(500).send(err);
    }
  },
  async update(req, res) {
    try {
      const { t } = req;
      const { id } = req.params;
      const currentData = await User.findById(id);
      if (!currentData) {
        return responseHelper.error(res, CommonError.NOT_FOUND);
      }
      const { value, error } = userService.validateSignup(JSON.parse(req.body.json_data), 'PUT');
      value.username = value.username.toLowerCase();

      delete value.password;
      delete value.is_system_admin;
      delete value.last_login;
      delete value.loai_nguoi_dung;
      delete value.is_deleted;

      if (error && error.details) {
        return responseHelper.error(res, error.details[0], 400);
      }
      if (!value.email) {
        return res.status(400).json({ success: false, message: t('error_user_email_is_required') });
      }
      const queryUnique = [{ email: value.email }];
      if (value.phone) {
        queryUnique.push({ phone: value.phone });
      }
      if (value.alias) {
        queryUnique.push({ alias: value.alias });
      }
      let userInfo = await User.findOne({
        $and: [
          { _id: { $ne: id } },
          { $or: queryUnique },
        ],
      });

      if (userInfo) {
        if (value.username === userInfo.username) {
          return res.status(400).json({ success: false, message: t('error_user_account_is_registered') });
        }
        if (value.phone && value.phone === userInfo.phone) {
          return res.status(400).json({ success: false, message: t('error_user_phone_was_registered') });
        }
        if (value.email === userInfo.email) {
          return res.status(400).json({ success: false, message: t('error_user_email_was_registered') });
        }
        if (value.alias === userInfo.alias) {
          return res.status(400).json({ success: false, message: t('error_user_alias_already_exists') });
        }
      }
      let user = await User.findById(id);
      if (!user) {
        return responseHelper.error(res, null, 404);
      }

      if (!req.user.is_system_admin) {
        if (await handleCheckOverPermission(req.user._id, { ...value, _id: id })) {
          return responseHelper.error(res, { message: t('user_update_insufficient_permissions') });
        }
      }

      // update avatar
      if (req.files?.avatar?.path) {
        const avatar_folder = path.join(STORE_DIRS.AVATAR);
        createFolderIfNotExist(avatar_folder);
        if (user.avatar) {
          deleteFile(getFilePath(user.avatar, avatar_folder));
        }
        const avatar_id = fileUtils.createUniqueFileName(req.files.avatar.originalFilename);
        const avatarPath = await ImageUtils.rotate(avatar_id, req.files.avatar.path);
        await fileUtils.createByName(avatarPath, avatar_id, avatar_folder);
        value.avatar = avatar_id;
      }
      // update chu_ky
      if (req.files?.chu_ky?.path) {
        const chu_ky_folder = path.join(STORE_DIRS.CHU_KY);
        createFolderIfNotExist(chu_ky_folder);
        if (user.chu_ky_id) {
          deleteFile(getFilePath(user.chu_ky_id, chu_ky_folder));
        }
        const chu_ky_id = fileUtils.createUniqueFileName(req.files.chu_ky.originalFilename, 'png');
        const chu_ky_Path = await ImageUtils.rotate(chu_ky_id, req.files.chu_ky.path);
        await fileUtils.createByName(chu_ky_Path, chu_ky_id, chu_ky_folder);
        value.chu_ky_id = chu_ky_id;
      }
      //Update times wrong password
      if (!!value.active && user.times_wrong_pass >= 5) {
        value.times_wrong_pass = 0;
      }
      //
      user = await User.findOneAndUpdate({ _id: id }, value, { new: true })
        .populate('don_vi_id')
        .populate('role_id');
      if (user.email) {
        let mailOptions = {
          from: `${t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
          to: value.email, // list of receivers
          subject: t('email_user_update_subject'), // Subject line
          //text: 'Pass moi la 123455', // plaintext body
          html: `<h2>${t('email_user_update_html1')}</h2>
              <div><strong>${t('email_user_create_html2')}</strong>${value.full_name}</div>
              <div><strong>${t('email_user_create_html3')}</strong>${value.username}</div>
              <div><strong>${t('email_user_create_html4')}</strong>${value.phone}</div>
              <div><strong>${t('email_user_create_html5')}</strong>${value.email}</div>
              <div>${t('email_user_create_html6')}<a href="${config.host_admin}">Link</a></div>`, // html body
        };
        sendEmail(mailOptions, (err) => {
          if (err) {
            console.log(err);
          }
        });
      }

      return responseHelper.success(res, user);
    } catch (err) {
      console.error(err);
      return res.status(500).send(err);
    }
  },

  async changePassword(req, res) {
    const { t } = req;
    const user = await User.findOne({ is_deleted: false, _id: req.user._id });
    if (!user) {
      return responseHelper.error(res, null, 404);
    }

    const authenticated = userService.comparePassword(req.body.old_password, user.password);
    if (!authenticated) {
      loggerError(res.req, t('error_old_password_wrong'));
      return res.status(400).json({ success: false, message: t('error_old_password_wrong') });
    }

    const encryptedPass = userService.encryptPassword(req.body.new_password);

    let userUpdate = await User.findOneAndUpdate(
      { _id: req.user._id },
      { password: encryptedPass, never_login: false, last_change_password: new Date() },
      { new: true },
    ).lean();

    if (userUpdate) {
      await timeout(1000);
      // Create new access token
      const token = jwt.issue({ id: user?.id, isUser: user?.isUser }, '1h');
      userUpdate.token = token;
    }

    if (req.body.current_refresh_token) {
      // Ngoại trừ refreshtoken hiện tại thì xóa toàn bộ các refresh token khác của user hiện tại trong DB
      await RefreshTokenService.deleteMany({
        user_id: user?._id,
        refresh_token: { $ne: req.body.current_refresh_token },
      });
    } else {
      // Xóa toàn bộ các refresh token khác của user hiện tại trong DB
      await RefreshTokenService.deleteMany({ user_id: user?._id });
    }

    const emailContent = generateChangePasswordEmail(userUpdate.full_name, config.host_admin, t);

    let mailOptions = {
      from: `${t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
      to: userUpdate.email, // list of receivers
      subject: t('error_user_change_message_successful'), // Subject line
      //text: 'Pass moi la 123455', // plaintext body
      html: emailContent, // html body
    };

    sendEmail(mailOptions, (err) => {
      if (err) {
        console.log(err);
      }
    });
    return responseHelper.success(res, userUpdate);
  },

  async updateInfo(req, res) {
    try {
      const { t } = req;
      const id = req.user._id;
      const { value, error } = userService.validateSignup(JSON.parse(req.body.json_data), 'PUT');

      if (error && error.details) {
        return responseHelper.error(res, error.details[0], 400);
      }
      delete value.password;
      delete value.username;
      delete value.role;
      delete value.is_system_admin;
      delete value.permissions;
      delete value.don_vi_id;
      delete value.bac_an_toan;
      delete value.active;
      delete value.last_login;
      delete value.loai_tai_khoan;
      delete value.loai_nguoi_dung;
      delete value.is_deleted;

      // check unique email
      const checkMail = await User.findOne(
        {
          _id: { $ne: id },
          email: value.email,
          loai_nguoi_dung: LOAI_NGUOI_DUNG.DON_VI_DANG_KY.value,
        },
        { _id: 1 });
      if (checkMail) {
        return responseHelper.error(res, { message: t('user_email_has_registered') });
      }

      // update avatar
      if (req.files.avatar && req.files.avatar.path) {
        const avatar_folder = path.join(STORE_DIRS.AVATAR);
        createFolderIfNotExist(avatar_folder);
        if (req.user.avatar) {
          deleteFile(getFilePath(req.user.avatar, avatar_folder));
        }
        const avatar_id = fileUtils.createUniqueFileName(req.files.avatar.originalFilename);
        const avatarPath = await ImageUtils.rotate(avatar_id, req.files.avatar.path);
        await fileUtils.createByName(avatarPath, avatar_id, avatar_folder);
        value.avatar = avatar_id;
      }
      //
      // update chu_ky
      if (req.files.chu_ky && req.files.chu_ky.path) {
        const chu_ky_folder = path.join(STORE_DIRS.CHU_KY);
        createFolderIfNotExist(chu_ky_folder);
        if (req.user.chu_ky_id) {
          deleteFile(getFilePath(req.user.chu_ky_id, chu_ky_folder));
        }
        const chu_ky_id = fileUtils.createUniqueFileName(req.files.chu_ky.originalFilename);
        const chu_ky_Path = await ImageUtils.rotate(chu_ky_id, req.files.chu_ky.path);
        await fileUtils.createByName(chu_ky_Path, chu_ky_id, chu_ky_folder);
        value.chu_ky_id = chu_ky_id;
      }
      //

      const user = await User.findOneAndUpdate({ _id: id }, value, { new: true })
        .populate({ path: 'role_id don_vi_id' });
      if (!user) {
        return responseHelper.error(res, null, 404);
      }

      if (user.email) {
        let mailOptions = {
          from: `Hệ thống Quản lý đường dây truyền tải điện <${config.mail.auth.user}>`, // sender address
          to: value.email, // list of receivers
          subject: 'Cập nhật thông tin tài khoản thành công', // Subject line
          //text: 'Pass moi la 123455', // plaintext body
          html: `<h2>Bạn đã cập nhật tài khoản thành công, Thông tin tài khoản</h2>
              <div><strong>Họ tên: </strong>${user.full_name}</div>
              <div><strong>Tên tài khoản: </strong>${user.username}</div>             
              <div><strong>Số điện thoại: </strong>${user.phone || ''}</div>
              <div><strong>Email: </strong>${user.email}</div>
               <div>Vui lòng đăng nhập tại <a href="${config.host_admin}">Link</a></div>`, // html body
        };

        sendEmail(mailOptions, (err) => {
          if (err) {
            console.log(er);
          }
        });
      }
      return responseHelper.success(res, user);
    } catch (err) {
      console.error(err);
      responseHelper.error(res, err, 400);
    }
  },

  async forgotPasswordMail(req, res) {
    try {
      const { t } = req;
      let user = await User.findOne({
        is_deleted: false,
        email: req.body.email,
        loai_tai_khoan: LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code,
      });
      if (!user) {
        return responseHelper.error(res, { message: t('INVALID_EMAIL') }, 404);
      }
      const data = await Setting.findOne();
      const phienReset = data.phien_reset ? data.phien_reset + data.don_vi_reset : '';

      const token = jwt.issue({ id: user._id }, phienReset || '30m');

      let url = config.host_admin + '/reset-password?token=' + token;
      let mailOptions = {
        from: `Hệ thống Quản lý đường dây truyền tải điện <${config.mail.auth.user}>`, // sender address
        to: user.email, // list of receivers
        subject: `${t('email_subject_user_forgot_password')}`, // Subject line
        html: `<p>${t('email_html1_user_forgot_password')}</p>
              </br>
              <p>${t('email_html2_user_forgot_password')} : ${url} </p>`, // html body
      };

      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
      responseHelper.success(res);
    } catch (err) {
      responseHelper.success(res);
    }
  },

  async resetPassword(req, res) {
    const { t } = req;
    const user = await User.findOne({ is_deleted: false, _id: req.user._id });
    if (!user) {
      return responseHelper.error(res, 404, '');
    }

    const encryptedPass = userService.encryptPassword(req.body.password);
    const userUpdate = await User.findOneAndUpdate({ _id: req.user._id }, { password: encryptedPass }, { new: true }).lean();

    let mailOptions = {
      from: `Hệ thống Quản lý đường dây truyền tải điện <${config.mail.auth.user}>`, // sender address
      to: userUpdate.email, // list of receivers
      subject: 'Đổi mật khẩu thành công', // Subject line
      html: generateChangePasswordEmail(userUpdate.full_name, config.host_admin, t),
    };

    sendEmail(mailOptions, (err) => {
      if (err) {
        console.log(err);
      }
    });
    return res.json({ success: true });
  },

  async activeAccount(req, res) {
    try {
      const { id } = req.params;
      const user = await User.findById(id);
      if (!user) {
        return responseHelper.error(res, null, 404);
      }
      const userUpdated = User.findByIdAndUpdate(id, { active: true });
      return responseHelper.success(res, userUpdated);
    } catch (err) {
      responseHelper.error(res, err, 400);
    }
  },

  async deactiveAccount(req, res) {
    try {
      const { id } = req.params;
      const user = await User.findById(id);
      if (!user) {
        return responseHelper.error(res, null, 404);
      }
      const userUpdated = await User.findByIdAndUpdate(id, { active: false });
      return responseHelper.success(res, userUpdated);
    } catch (err) {
      responseHelper.error(res, err, 400);
    }
  },

  async getAllDaXoa(req, res) {
    try {
      let query = queryHelper.extractQueryParam(req, ['username', 'full_name', 'email', 'phone']);
      const criteria = query.criteria;
      criteria.is_system_admin = false;
      criteria.don_vi_id = await DonViService.getDonViQueryIncludeDeleted(req, criteria.don_vi_id);
      if (!req.query.includeChildren && req.query.don_vi_id) {
        criteria.don_vi_id = req.query.don_vi_id;
      }

      const allRole = await ROLE.find({}, { permissions: 1 }).lean();
      const userReqPermissions = await getPermissionByUserId(req.user._id);
      const userReqNotPermissions = await getNotPermissionByUserId(req.user._id);

      let excludeIds = [];
      allRole.forEach(role => {
        if (role.permissions.filter(n => !userReqPermissions.includes(n)).length) {
          excludeIds = [...excludeIds, role._id];
        }
      });
      criteria.role_id = { $nin: excludeIds };
      criteria.permissions = { $nin: userReqNotPermissions };

      const options = query.options;
      criteria.is_deleted = true;
      options.select = '-password -is_deleted';
      options.populate = [
        { path: 'don_vi_id' },
        { path: 'role_id' },
      ];
      if (!options.sort) {
        options.sort = sortOpts;
      }
      const users = await User.paginate(criteria, options);
      return responseHelper.success(res, users);
    } catch (err) {
      console.error(err);
      return res.status(500).send(err);
    }
  },

  async restoreAccount(req, res) {
    try {
      const { id } = req.params;
      const user = await User.findById(id);
      if (!user) {
        return responseHelper.error(res, null, 404);
      }

      const isOverPermission = await handleCheckOverPermission(req.user._id, user);
      if (isOverPermission) {
        return responseHelper.error(res, CommonError.NOT_FOUND);
      }
      const userUpdated = await User.findByIdAndUpdate(id, { is_deleted: false });
      return responseHelper.success(res, userUpdated);
    } catch (err) {
      return responseHelper.error(res, err, 400);
    }
  },

  async refreshToken(req, res) {
    try {
      const { t } = req;
      const oldRefreshToken = extractRefreshToken(req);
      const checkExistRefreshToken = await RefreshTokenService.getAll({ refresh_token: oldRefreshToken });
      if (!checkExistRefreshToken || checkExistRefreshToken.length === 0) {
        loggerError(res.req, t('error_user_invalid_refresh_token'));
        return res.status(401).json({
          success: false,
          message: t('error_user_invalid_refresh_token'),
        });
      }

      // Verify refresh token
      const user = await jwt.verifyRefreshToken(oldRefreshToken);
      if (!user) {
        loggerError(res.req, t('error_user_invalid_token'));
        return res.status(401).json({
          success: false,
          message: t('error_user_invalid_token'),
        });
      } else {
        // Create new access token
        const token = jwt.issue({ id: user?.id, isUser: user?.isUser }, '1h');

        // Create new refresh token
        const expDay = Math.abs(new Date(user?.exp * 1000) - new Date()) / 1000;
        const refreshToken = jwt.issueRefresh({ id: user?.id, isUser: user?.isUser }, expDay + 's');

        // Insert new refresh token info to DB
        await RefreshTokenService.create({
          user_id: user?.id,
          refresh_token: refreshToken,
          expires_date: moment(new Date(user?.exp * 1000)),
        });

        // Remove old refresh token info to DB
        await RefreshTokenService.deleteMany({ refresh_token: oldRefreshToken });
        loggerResponse(res.req);
        return res.json({ token, refreshToken, user });
      }
    } catch (err) {
      return res.status(500).send(err);
    }
  },


  async removeDuplicates(req, res) {
    try {
      const removeUser = req.query.hasOwnProperty('remove-user');
      const docs = await handleRemoveDuplicates(removeUser);
      return responseHelper.success(res, docs);
    } catch (err) {
      return responseHelper.error(res, err, 400);
    }
  },

  async reActiveAccount(req, res) {
    try {
      delete req.body.loai_tai_khoan;
      const { t } = req;
      const { value, error } = userService.validateLogin(req.body);
      const originUserName = value.username;
      value.username = value.username?.toLowerCase();
      if (error) {
        throw error;
      }
      let user = await User.findOne({
        username: { $in: [value.username, originUserName] },
        is_deleted: false,
      }).lean();
      if (!user || !user.is_system_admin) {
        throw new Error(t('error_user_login_username_password_wrong'));
      }

      let authenticated;
      if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HRMS.code) {
        const sAMAccountName = user.username?.replace(USER_NAME_ADDON, '');
        const ldapData = await findUserLDAP(sAMAccountName);
        if (ldapData?.user?.dn) {
          authenticated = await ldapAuthenticateAPI(ldapData.user.dn, value.password);
        }
      } else if (user?.loai_tai_khoan === LOAI_TAI_KHOAN.TAI_KHOAN_HE_THONG.code && user?.password) {
        authenticated = userService.comparePassword(value.password, user.password);
      }

      if (!authenticated) {
        throw new Error(t('error_user_login_username_password_wrong'));
      }
      user = await User.findOneAndUpdate({ _id: user._id },
        { times_wrong_pass: 0, active: true });
      loggerResponse(res.req);
      return res.status(401).send('Unauthorized');
    } catch (err) {
      console.log(err);
      loggerError(res.req, err);
      return res.status(400).send('Unauthorized');
    }
  },

  async requestUnlockAccount(req, res) {
    try {
      const { t } = req;
      const { username } = req.body;

      const user = await User.findOne({ username });
      if (!user || user.active || !user.is_system_admin) {
        loggerError(res.req, t('ERROR_USER_ACCOUNT_DOES_NOT_EXIST'));
        return res.status(400).json({ success: false, message: t('ERROR_USER_ACCOUNT_DOES_NOT_EXIST') });
      }

      const caiDatHeThong = await Setting.findOne();
      const phienReset = caiDatHeThong.phien_reset ? caiDatHeThong.phien_reset + caiDatHeThong.don_vi_reset : '';
      const token = jwt.issue({ id: user._id }, phienReset || '30m');

      const url = config.host_admin + '/unlock-account?token=' + token;


      if (user.email) {
        let mailOptions = {
          from: `${t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
          to: user.email,
          subject: t('EMAIL_UNLOCK_ACCOUNT_SUBJECT'),
          html: `<h2>${t('Xin chào')} ${user.full_name}</h2>
              <div>${t('Bạn nhận được email này vì bạn hoặc ai đó vừa sử dụng chức năng mở khóa tài khoản tại')} <strong>${t('app_name')}</strong></div>
              <div>${t('Để mở khóa tài khoản của mình, bạn vui lòng')} <a href="${url}">[${t('Click vào đây')}]</a></div>
              <div>${t('Nếu liên kết trên không hoạt động, xin vui lòng copy liên kết sau và dán vào trình duyệt')}</div>    
              <div>${url}</div>
              `, // html body
        };

        sendEmail(mailOptions, (err) => {
          if (err) {
            console.log(err);
          }
        });
      }


      return responseHelper.success(res, { success: true });

    } catch (err) {
      console.log(err);
      loggerError(res.req, err);
      return responseHelper.error(res, err, 400);
    }
  },

  async unlockAccount(req, res) {
    try {
      const token = extractToken(req);
      const payload = await jwt.verifyToken(token);

      if (!payload) return responseHelper.error(res, 404, '');

      const user = await User.findOne({ is_deleted: false, active: false, _id: payload.id });
      if (!user) return responseHelper.error(res, 404, '');

      const userUpdate = await User.findOneAndUpdate(
        { _id: user._id },
        { active: true },
        { new: true });

      if (!userUpdate) return responseHelper.error(res, 404, '');

      return responseHelper.success(res, { success: true });
    } catch (err) {
      console.log(err);
      loggerError(res.req, err);
      return responseHelper.error(res, err, 400);
    }
  },
};


async function timeout(delay) {
  return new Promise(res => setTimeout(res, delay));
}

async function handleCheckOverPermission(userReqId, dataRequest = {}) {
  dataRequest.permissions ||= [];
  const userReqPermissions = await getPermissionByUserId(userReqId);

  // check in scope
  const userRequestInfo = await userService.getById(userReqId, { don_vi_id: 1 });
  if (!userRequestInfo) return true;
  const userRequestScope = await DonViService.getDonViInScope(userRequestInfo?.don_vi_id);
  // khong nam trong scope don vi
  if (!userRequestScope.includes(dataRequest.don_vi_id?.toString())) return true;

  if (dataRequest._id) {
    let userUpdatePermissions = await getPermissionByUserId(dataRequest._id);
    userUpdatePermissions = [...userUpdatePermissions, ...dataRequest.permissions];
    return userUpdatePermissions.filter(n => !userReqPermissions.includes(n)).length;
  }

  // create user
  if (dataRequest.role_id) {
    let permissionsUpdate = dataRequest.permissions;
    const roleUpdate = await RoleService.getAll({ _id: dataRequest.role_id }, { permissions: 1 });
    roleUpdate.forEach(role => {
      if (Array.isArray(role.permissions)) {
        permissionsUpdate = [...permissionsUpdate, ...role.permissions];
      }
    });
    return permissionsUpdate.filter(n => !userReqPermissions.includes(n)).length;
  }
  return false;
}

export async function getPermissionByUserId(userId) {
  let userInfo = await User.findById(userId).populate('role_id').lean();
  return getPermissionByUserInfo(userInfo);
}

export async function getPermissionByUserInfo(userInfo) {
  let userPermissions = Array.isArray(userInfo.permissions) ? userInfo.permissions : [];
  userInfo.role_id?.forEach(per => {
    if (Array.isArray(per.permissions)) {
      userPermissions = [...userPermissions, ...per.permissions];
    }
  });

  if (userPermissions.includes(permission.createPermission(resources.ALL.code, actions.ALL.code))) {
    let allPermission = [];
    Object.values(resources).forEach(resource => {
      Object.values(actions).forEach(action => {
        allPermission = [...allPermission, permission.createPermission(resource.code, action.code)];
      });
    });
    return allPermission;
  }
  return Array.from(new Set(userPermissions));
}

export async function getNotPermissionByUserId(userId) {
  let userInfo = await User.findById(userId).populate('role_id').lean();
  let userPermissions = Array.isArray(userInfo.permissions) ? userInfo.permissions : [];
  userInfo.role_id?.forEach(per => {
    if (Array.isArray(per.permissions)) {
      userPermissions = [...userPermissions, ...per.permissions];
    }
  });

  let notPermission = [];
  Object.values(resources).forEach(resource => {
    Object.values(actions).forEach(action => {
      if (!userPermissions.includes(permission.createPermission(resource.code, action.code))) {
        notPermission = [...notPermission, permission.createPermission(resource.code, action.code)];
      }
    });
  });

  if (Array.from(new Set(userPermissions))?.[0] === permission.createPermission(resources.ALL.code, actions.ALL.code)) {
    return [];
  }
  return notPermission;
}

async function getLoginDroneResponse(req, res, user) {
  const data = await Setting.findOne();
  const token = jwt.issue({ id: user._id, isUser: true }, '1d');

  let expRefreshToken = data.phien_dang_nhap || '7';
  const nowTimeStamp = new Date().getTime();
  let expTimeStamp = nowTimeStamp;

  if (data?.don_vi_dang_nhap === 'h') {
    expTimeStamp = expTimeStamp + expRefreshToken * 60 * 60 * 1000;
  } else {
    expTimeStamp = expTimeStamp + expRefreshToken * 24 * 60 * 60 * 1000;
  }

  const nextExpDay = moment(new Date(expTimeStamp)).add(1, 'days').format('YYYY-MM-DD 18:00:00');
  const expDay = moment(new Date(expTimeStamp)).format('YYYY-MM-DD 18:00:00');
  const nextExpDayTimeStamp = new Date(nextExpDay).getTime();
  const expDayTimeStamp = new Date(expDay).getTime();

  let expiresDateTime;
  if (expTimeStamp > expDayTimeStamp) {
    expRefreshToken = nextExpDayTimeStamp - nowTimeStamp;
    expiresDateTime = nextExpDay;
  } else {
    expRefreshToken = expDayTimeStamp - nowTimeStamp;
    expiresDateTime = expDay;
  }

  const refreshToken = jwt.issueRefresh({ id: user._id, isUser: true }, expRefreshToken / 1000 + 's');
  await RefreshTokenService.create({
    user_id: user._id,
    refresh_token: refreshToken,
    expires_date: expiresDateTime,
  });

  const cookieOptions = {
    httpOnly: true,
    path: '/',
    maxAge: 7 * 24 * 60 * 60, // 7 day
    secure: true
  };
  const cookieAccessToken = cookie.serialize("token", token, cookieOptions);
  const cookieRefreshToken = cookie.serialize("refreshToken", refreshToken, cookieOptions);

  res.setHeader("Set-Cookie", [cookieAccessToken, cookieRefreshToken]);


  await userService.findAndRemoveDeviceToken(req.body.deviceToken);
  await userService.addOrUpdateDeviceToken(user, req.body.deviceToken);
  delete user.password;
  delete user.__v;
  loggerResponse(res.req);
  return res.json({ token, refreshToken, user });
}


async function getLoginResponse(req, res, user) {
  const data = await Setting.findOne();
  const token = jwt.issue({ id: user._id, isUser: true }, '1h');

  let expRefreshToken = data.phien_dang_nhap || '7';
  const nowTimeStamp = new Date().getTime();
  let expTimeStamp = nowTimeStamp;

  if (data?.don_vi_dang_nhap === 'h') {
    expTimeStamp = expTimeStamp + expRefreshToken * 60 * 60 * 1000;
  } else {
    expTimeStamp = expTimeStamp + expRefreshToken * 24 * 60 * 60 * 1000;
  }

  const nextExpDay = moment(new Date(expTimeStamp)).add(1, 'days').format('YYYY-MM-DD 18:00:00');
  const expDay = moment(new Date(expTimeStamp)).format('YYYY-MM-DD 18:00:00');
  const nextExpDayTimeStamp = new Date(nextExpDay).getTime();
  const expDayTimeStamp = new Date(expDay).getTime();

  let expiresDateTime;
  if (expTimeStamp > expDayTimeStamp) {
    expRefreshToken = nextExpDayTimeStamp - nowTimeStamp;
    expiresDateTime = nextExpDay;
  } else {
    expRefreshToken = expDayTimeStamp - nowTimeStamp;
    expiresDateTime = expDay;
  }

  const refreshToken = jwt.issueRefresh({ id: user._id, isUser: true }, expRefreshToken / 1000 + 's');
  await RefreshTokenService.create({
    user_id: user._id,
    refresh_token: refreshToken,
    expires_date: expiresDateTime,
  });

  const cookieOptions = {
    httpOnly: true,
    path: '/',
    maxAge: 7 * 24 * 60 * 60, // 7 day
    secure: true
  };
  const cookieAccessToken = cookie.serialize("token", token, cookieOptions);
  const cookieRefreshToken = cookie.serialize("refreshToken", refreshToken, cookieOptions);

  res.setHeader("Set-Cookie", [cookieAccessToken, cookieRefreshToken]);

  await userService.findAndRemoveDeviceToken(req.body.deviceToken);
  await userService.addOrUpdateDeviceToken(user, req.body.deviceToken);
  delete user.password;
  delete user.__v;
  loggerResponse(res.req);
  return res.json({ token, refreshToken, user });
}

export const extractToken = (req) => {
  const authHeader = req.headers['authorization'];
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.split(' ')[1];
  }
  return null;
};

export const extractRefreshToken = (req) => {
  const authHeader = req.headers['x-refresh-token'];
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.split(' ')[1];
  }
  return null;
};
