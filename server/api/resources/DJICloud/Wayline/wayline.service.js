import Model from './wayline.model';
import * as ValidatorHelper from '../../../helpers/validatorHelper';

const Joi = require('joi');
const objSchema = Joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function markFavorite(waylineIds, isFavorite) {
  const ids = await Model.count({ _id: { $in: waylineIds } });
  if (ids !== waylineIds.length) {
    throw new Error('Invalid wayline id(s)');
  }

  return Model.updateMany({ _id: { $in: waylineIds } }, {
    favorited: isFavorite,
  });
}
