import * as amazonService from './oss/amazon.service';
import * as minioService from './oss/minio.service';
import { getConfig } from '../../../../config/config';

const config = getConfig(process.env.NODE_ENV);
const ossType = config?.oss?.enable || 'minio';

const storageConfig = {
  aws: amazonService,
  minio: minioService,
};

const Service = storageConfig[ossType];
const bucketConfig = config.oss.bucket;

export const getObjectUrl = async (objectKey, bucket = bucketConfig) => {
  if (!objectKey) throw new Error('Object key are required');
  return Service.getObjectUrl(objectKey, bucket);
};

export const deleteObject = (objectKey, bucket = bucketConfig) => Service.deleteObject(objectKey, bucket);

export const getObject = (objectKey, bucket = bucketConfig) => Service.getObject(objectKey, bucket);

export const putObject = (objectKey, body, bucket = bucketConfig) => Service.putObject(objectKey, body, bucket);

export const getSTSCredentials = (token) => Service.getSTSCredentials(token);
