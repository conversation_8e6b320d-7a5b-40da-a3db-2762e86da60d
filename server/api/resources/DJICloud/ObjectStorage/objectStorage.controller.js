import * as Service from './objectStorage.service';
import * as djiResponseHelper from '../../../helpers/djiResponseHelper';
import jwt from '../../../helpers/jwt';
import * as userService from '../../User/user.service';

export const getTemporaryCredential = async (req, res) => {
  try {
    const token = req.headers['authorization'] || `Bearer ${req.headers['x-auth-token']}`;
    const stsCredentials = await Service.getSTSCredentials(token);
    return djiResponseHelper.success(res, stsCredentials);
  } catch (err) {
    return djiResponseHelper.error(res, err);
  }
};

export const checkCredential = async (req, res) => {
  try {
    const { token } = req.query;
    if (!token) {
      return res.status(403).send({ reason: 'Token is required' });
    }

    const payload = await jwt.verifyToken(token.split(' ')[1]);
    if (!payload) {
      return res.status(403).send({ reason: 'Invalid Token' });
    }

    const user = await userService.getOne({ is_deleted: false, _id: payload.id });
    if (!user) {
      return res.status(403).send({ reason: 'User Not Found' });
    }

    return res.status(200).send({
      user: user.full_name,
      maxValiditySeconds: 3600 * 24, // 24 hours
      claims: {
        exp: Math.floor(Date.now() / 1000) + 3600 * 24, // 24 hours
      },
    });
  } catch (err) {
    return res.status(403).send({ reason: err.message });
  }
};
