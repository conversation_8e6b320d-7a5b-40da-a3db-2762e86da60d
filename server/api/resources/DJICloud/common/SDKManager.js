import { GATEWAY_TYPE } from '../../../constant/constant';
import * as deviceDictionaryService from '../Manage/DeviceDictionary/deviceDictionary.service';

const SDK_MAP = new Map();

export function getDeviceSDK(gatewaySn) {
  if (SDK_MAP.has(gatewaySn)) {
    return SDK_MAP.get(gatewaySn);
  }
  return null;
}

export async function registerDevice(gateway) {
  if (!gateway || !gateway.gateway_sn) return;

  const dictionary = await deviceDictionaryService.getOne({
    domain: gateway.domain,
    type: gateway.type,
    sub_type: gateway.sub_type,
  });

  if (!dictionary || !GATEWAY_TYPE.includes(dictionary.code.toString())) {
    return null;
  }

  const registeredDevice = {
    gateway_sn: gateway.gateway_sn,
    drone_sn: gateway.drone_sn,
    gateway_type: dictionary.code,
    gateway_thing_version: gateway.gateway_thing_version,
    drone_thing_version: gateway.drone_thing_version,
  };

  SDK_MAP.set(gateway.gateway_sn, registeredDevice);
  return registeredDevice;
}

export function logoutDevice(gatewaySn) {
  SDK_MAP.delete(gatewaySn);
}
