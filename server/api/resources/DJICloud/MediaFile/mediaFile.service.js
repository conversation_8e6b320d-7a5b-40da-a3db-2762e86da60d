import FormData from 'form-data';
import axios from 'axios';
import Model from './mediaFile.model';
import * as ValidatorHelper from '../../../helpers/validatorHelper';

const joi = require('joi');
const objSchema = joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function uploadTuDongPhanLoai(phieuGiaoViecId, buffer, name, token, contentType) {
  let data = new FormData();
  data.append('phieu_giao_viec_id', phieuGiaoViecId.toString());
  data.append('image', buffer, {
    filename: name,
    contentType: contentType,
  });

  let config = {
    method: 'post',
    url: 'http://localhost:3000/api/anhvitri/tudongphanloai',
    headers: {
      ...data.getHeaders(),
      Authorization: token,
    },
    data: data,
  };

  return axios.request(config)
    .then((response) => {
      if (response.status === 200) {
        return response?.data?.data;
      }
      return false;
    })
    .catch((error) => {
      return false;
    });

}

export async function getOne(query) {
  return Model.findOne(query).lean();
}

export async function deleteOne(query) {
  return Model.remove(query);
}
