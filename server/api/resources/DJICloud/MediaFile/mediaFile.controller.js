import Model from './mediaFile.model';
import * as djiResponseHelper from '../../../helpers/djiResponseHelper';
import * as phieuGiaoViecService from '../../QuanLyVanHanh/PhieuGiaoViec/phieuGiaoViec.service';
import * as deviceDictionaryService from '../Manage/DeviceDictionary/deviceDictionary.service';
import { createUniqueFileName, deleteFile, getDirPath, getFilePath } from '../../../utils/fileUtils';
import { STORE_DIRS } from '../../../constant/constant';
import * as controllerHelper from '../../../helpers/controllerHelper';
import * as Service from '../../AI/ObjectDetection/objectDetection.service';
import { uploadTuDongPhanLoai } from './mediaFile.service';
import * as responseHelper from '../../../helpers/responseHelper';
import CommonError from '../../../error/CommonError';
import * as storageService from '../ObjectStorage/objectStorage.service';
import fs from 'fs';

const searchLikes = ['file_name', 'drone'];
const populateOpts = [{ path: 'nguoi_tao_id' }, { path: 'don_vi_id' }];
const sortOpts = { created_at: -1 };

export const getAll = controllerHelper.createGetAllFunction(Model, searchLikes, populateOpts, sortOpts, 'don_vi_id');
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);

export const remove = async (req, res) => {
  try {
    const { id } = req.params;

    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseHelper.error(res, CommonError.NOT_FOUND);
    }
    if (!data.anh_vi_tri_id) {
      deleteFile(getFilePath(mediaFile.file_name, getDirPath(mediaFile.don_vi_id.toString(), STORE_DIRS.DRONE_MEDIA)));
    }

    return responseHelper.success(res, data);
  } catch (err) {
    return responseHelper.error(res, err);
  }
};

export async function mediaFastUpload(req, res) {
  const { fingerprint } = req.body;

  const mediaFile = await Model.findOne({ fingerprint });

  if (!mediaFile) {
    return djiResponseHelper.error(res, `Fingerprint ${fingerprint} don't exist`);
  }

  return djiResponseHelper.success(res);
}

export async function getExistFileTinyFingerprint(req, res) {
  const { tiny_fingerprints } = req.body;

  const mediaFiles = await Model.find({ tinny_fingerprint: { $in: tiny_fingerprints } }).lean();
  const tinyFingerprints = mediaFiles.map(file => file.tinny_fingerprint);

  return djiResponseHelper.success(res, { tiny_fingerprints: tinyFingerprints });
}

export async function mediaUploadCallback(req, res) {
  const { workspace_id } = req.params;
  const { don_vi_id, _id } = req.user;
  const param = req.body;
  const { name, object_key } = param;
  const token = req.headers['authorization'] || `Bearer ${req.headers['x-auth-token']}`;

  try {
    const phieuGiaoViec = await phieuGiaoViecService.getOne({ uuid: workspace_id });
    if (!phieuGiaoViec) {
      return djiResponseHelper.error(res, 'Phieu giao viec not found');
    }

    const mediaTimeCreate = new Date(param.metadata?.created_time);
    const beginTime = new Date(phieuGiaoViec.thoi_gian_cong_tac_bat_dau);
    const endTime = new Date(phieuGiaoViec.thoi_gian_cong_tac_ket_thuc);

    if (beginTime > mediaTimeCreate || mediaTimeCreate > endTime) {
      return djiResponseHelper.error(res, 'Media create time is not in the range of task time');
    }

    const [domain, type, sub_type] = param?.ext?.payload_model_key.split('-');
    const payload = await deviceDictionaryService.getOne({ domain, type, sub_type });

    const objectStream = await storageService.getObject(object_key);
    const contentType = objectStream.ContentType;
    const chunks = [];
    for await (const chunk of objectStream.Body) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    const uniqueFileName = createUniqueFileName(name);
    let anhViTri = null;

    if (!contentType.startsWith('image/')) {
      const filePath = getFilePath(uniqueFileName, getDirPath(don_vi_id.toString(), STORE_DIRS.DRONE_MEDIA));
      fs.writeFileSync(filePath, buffer);
    } else {
      anhViTri = await uploadTuDongPhanLoai(phieuGiaoViec._id, buffer, name, token, contentType);
      if (!anhViTri) {
        return djiResponseHelper.error(res, 'Anh vi tri not found');
      }
    }

    const query = {
      file_name: uniqueFileName,
      file_path: param?.path,
      fingerprint: param?.fingerprint,
      tinny_fingerprint: param?.ext?.tinny_fingerprint,
      sub_file_type: param?.sub_file_type,
      is_original: param?.ext.is_original,
      drone: param?.ext?.sn,
      payload: payload?.device_name,
      don_vi_id: don_vi_id,
      nguoi_tao_id: _id,
      anh_vi_tri_id: anhViTri?._id,
      phieu_giao_viec_id: phieuGiaoViec?._id,
    };
    const mediaFile = await Model.create(query);

    return djiResponseHelper.success(res, mediaFile);
  } catch (err) {
    return djiResponseHelper.error(res, err);
  } finally {
    storageService.deleteObject(object_key);
  }
}

export async function getFilesList(req, res) {
  try {
    const { don_vi_id } = req.user;
    const { page, page_size, order_by, key, ...query } = req.query;

    query.is_deleted = false;
    delete query.file_type;
    delete query.action_type;

    const criteria = {
      ...query,
      don_vi_id: don_vi_id,
    };

    let sort = {};

    if (order_by) {
      let [field, direction] = order_by.trim().split(/\s+/);
      if (field === 'update_time') field = 'updated_at';
      if (field === 'create_time') field = 'created_at';
      sort[field] = direction && direction.toLowerCase() === 'desc' ? -1 : 1;
    } else {
      sort = sortOpts;
    }

    const options = {
      collation: { locale: 'vi' },
      lean: true,
      limit: +page_size,
      page: +page,
      sort: sort,
    };

    const paginate = await Model.paginate(criteria, options);

    const list = paginate.docs.map((item) => ({
      ...item,
      update_time: item.updated_at.getTime(),
      create_time: item.created_at.getTime(),
    }));

    const data = {
      list: list,
      pagination: {
        page: paginate.page,
        total: paginate.totalDocs,
        page_size: paginate.limit,
      },
    };

    return djiResponseHelper.success(res, data);
  } catch (err) {
    return djiResponseHelper.error(res, err);
  }
}

export async function getFileUrl(req, res) {
  try {
    const { file_id } = req.params;
    const mediaFile = await Model.findOne({ _id: file_id }).populate('anh_vi_tri_id');
    if (!mediaFile) {
      return djiResponseHelper.error(res, 'Not Found');
    }

    let filePath;
    if (mediaFile.anh_vi_tri_id) {
      filePath = getFilePath(mediaFile.anh_vi_tri_id.image_id, mediaFile.anh_vi_tri_id.folder_path);
    } else {
      filePath = getFilePath(mediaFile.file_name, getDirPath(mediaFile.don_vi_id.toString(), STORE_DIRS.DRONE_MEDIA));
    }

    return res.download(filePath, mediaFile.file_name);
  } catch (e) {
    return djiResponseHelper.error(res, e);
  }
}
