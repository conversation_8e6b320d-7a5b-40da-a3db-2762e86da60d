class Chan {
  static CHANNEL = new Map();

  constructor() {
    this.data = null;
    this.resolve = null;
  }

  static getInstance(tid, isNeedCreate) {
    if (!isNeedCreate) {
      return this.CHANNEL.get(tid);
    }
    const chan = new <PERSON>();
    this.CHANNEL.set(tid, chan);
    return chan;
  }

  async get(tid, timeout) {
    const chan = Chan.CHANNEL.get(tid);
    if (!chan) {
      return null;
    }

    try {
      await new Promise((resolve, reject) => {
        chan.resolve = resolve;
        setTimeout(() => {
          resolve();
        }, timeout);
      });
    } finally {
      Chan.CHANNEL.delete(tid);
    }
    return chan.data;
  }

  put(response) {
    const chan = Chan.CHANNEL.get(response.tid);
    if (!chan) return;

    chan.data = response;
    if (chan.resolve) {
      chan.resolve();
    }
  }
}

export default Chan;
