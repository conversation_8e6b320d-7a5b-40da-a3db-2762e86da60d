import * as mqttService from '../mqtt.service';
import * as SDKManager from '../../common/SDKManager';
import { STATE_DATA_KEY } from '../../../../constant/constant';
import * as livestreamService from '../../Manage/Livestream/livestream.service';

export async function subscribe(gateway, unsubscribeSubDevice) {
  SDKManager.registerDevice(gateway);
  mqttService.subscribe(`thing/product/${gateway.gateway_sn}/state`);

  if (unsubscribeSubDevice) {
    mqttService.unsubscribe(`thing/product/${gateway.drone_sn}/state`);
    return;
  }

  if (gateway.drone_sn) {
    mqttService.subscribe(`thing/product/${gateway.drone_sn}/state`);
  }
}

export async function unsubscribe(gateway) {
  SDKManager.logoutDevice(gateway.gateway_sn);
  mqttService.unsubscribe(`thing/product/${gateway.gateway_sn}/state`);
  if (gateway.drone_sn) {
    mqttService.unsubscribe(`thing/product/${gateway.drone_sn}/state`);
  }
}

export async function stateDataRouterFlow(message, topic) {
  message.from = topic.substring(topic.indexOf('thing/product/') + 'thing/product/'.length, topic.indexOf('/state'));

  const deviceManager = SDKManager.getDeviceSDK(message.from);
  if (!deviceManager) return;

  const stateDataKey = STATE_DATA_KEY.find(new Set(Object.keys(message.data)), deviceManager.gateway_type);

  switch (stateDataKey) {
    case STATE_DATA_KEY.RC_AND_DRONE_FIRMWARE_VERSION:
      return rcAndDroneFirmwareVersionUpdate(message, topic);
    case STATE_DATA_KEY.RC_LIVE_CAPACITY:
      return rcLivestreamAbilityUpdate(message, topic);
    case STATE_DATA_KEY.RC_DRONE_CONTROL_SOURCE:
      return rcControlSourceUpdate(message, topic);
    case STATE_DATA_KEY.RC_LIVE_STATUS:
      return rcLiveStatusUpdate(message, topic);
    case STATE_DATA_KEY.RC_PAYLOAD_FIRMWARE:
      return rcPayloadFirmwareVersionUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_FIRMWARE_VERSION:
      return dockFirmwareVersionUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_LIVE_CAPACITY:
      return dockLivestreamAbilityUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_CONTROL_SOURCE:
      return dockControlSourceUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_LIVE_STATUS:
      return dockLiveStatusUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_WPMZ_VERSION:
      return dockWpmzVersionUpdate(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_THERMAL_SUPPORTED_PALETTE_STYLE:
      return dockThermalSupportedPaletteStyle(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_RTH_MODE:
      return dockDroneRthMode(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_CURRENT_RTH_MODE:
      return dockDroneCurrentRthMode(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_COMMANDER_MODE_LOST_ACTION:
      return dockDroneCommanderModeLostAction(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_CURRENT_COMMANDER_FLIGHT_MODE:
      return dockDroneCurrentCommanderFlightMode(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_COMMANDER_FLIGHT_HEIGHT:
      return dockDroneCommanderFlightHeight(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_MODE_CODE_REASON:
      return dockDroneModeCodeReason(message, topic);
    case STATE_DATA_KEY.DOCK_DRONE_OFFLINE_MAP_ENABLE:
      return dockDroneOfflineMapEnable(message, topic);
    case STATE_DATA_KEY.DOCK_AND_DRONE_DONGLE_INFOS:
      return dongleInfos(message, topic);
    case STATE_DATA_KEY.DOCK_SILENT_MODE:
      return dockSilentMode(message, topic);
    default:
      break;
  }
}

async function rcAndDroneFirmwareVersionUpdate(message, topic) {
  console.log('rcAndDroneFirmwareVersionUpdate not implemented');
}

function rcLivestreamAbilityUpdate(message, topic) {
  return livestreamService.saveLiveCapacity(message.data.live_capacity.device_list);
}

async function rcControlSourceUpdate(message, topic) {
  console.log('rcControlSourceUpdate not implemented');
}

function rcLiveStatusUpdate(message, topic) {
  console.log('rcLiveStatusUpdate not implemented');
}

async function rcPayloadFirmwareVersionUpdate(message, topic) {
  console.log('rcPayloadFirmwareVersionUpdate not implemented');
}

async function dockFirmwareVersionUpdate(message, topic) {
  console.log('dockFirmwareVersionUpdate not implemented');
}

function dockLivestreamAbilityUpdate(message, topic) {
  console.log('dockLivestreamAbilityUpdate not implemented');
}

async function dockControlSourceUpdate(message, topic) {
  console.log('dockControlSourceUpdate not implemented');
}

function dockLiveStatusUpdate(message, topic) {
  console.log('dockLiveStatusUpdate not implemented');
}

function dockWpmzVersionUpdate(message, topic) {
  console.log('dockWpmzVersionUpdate not implemented');
}

function dockThermalSupportedPaletteStyle(message, topic) {
  console.log('dockThermalSupportedPaletteStyle not implemented');
}

function dockDroneRthMode(message, topic) {
  console.log('dockRthMode not implemented');
}

function dockDroneCurrentRthMode(message, topic) {
  console.log('dockCurrentRthMode not implemented');
}

function dockDroneCommanderModeLostAction(message, topic) {
  console.log('dockDroneCommanderModeLostAction not implemented');
}

function dockDroneCurrentCommanderFlightMode(message, topic) {
  console.log('dockDroneCurrentCommanderFlightMode not implemented');
}

function dockDroneCommanderFlightHeight(message, topic) {
  console.log('dockDroneCommanderFlightHeight not implemented');
}

function dockDroneModeCodeReason(message, topic) {
  console.log('dockDroneModeCodeReason not implemented');
}

function dockDroneOfflineMapEnable(message, topic) {
  console.log('dockDroneOfflineMapEnable not implemented');
}

function dongleInfos(message, topic) {
  console.log('dongleInfos not implemented');
}

function dockSilentMode(message, topic) {
  console.log('dockSilentMode not implemented');
}

