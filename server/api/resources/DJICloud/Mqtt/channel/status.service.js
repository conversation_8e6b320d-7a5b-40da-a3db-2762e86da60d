import * as mqttService from '../mqtt.service';
import * as SDKManager from '../../common/SDKManager';
import * as deviceService from '../../Manage/Device/device.service';
import * as stateMqttService from './state.service';
import * as osdMqttService from './osd.service';
import * as servicesMqttService from './services.service';
import * as redisService from '../../Redis/redis.service';
import * as dictionaryService from '../../Manage/DeviceDictionary/deviceDictionary.service';
import * as mqttHelper from '../mqttHelper';
// import * as webSocketService from '../../WebSocket/webSocket.service';
import { ICON_URL } from '../../../../constant/constant';

// import * as manageService from '../../Manage/manage.service';

export async function publish(message, topic) {
  return mqttService.publishReply(message, topic);
}

export async function subscribe(gateway) {
  mqttService.subscribe(`sys/product/${gateway.gateway_sn}/status`);
}

export async function unsubscribe(gateway) {
  SDKManager.logoutDevice(gateway.gateway_sn);
  mqttService.unsubscribe(`sys/product/${gateway.gateway_sn}/status`);
}

export async function statusRouterFlow(message, topic) {
  message.from = topic.substring(topic.indexOf('sys/product/') + 'sys/product/'.length, topic.indexOf('/status'));
  const routeStatus = message?.data?.sub_devices?.length === 0;

  return routeStatus
    ? updateTopoOffline(message, topic)
    : updateTopoOnline(message, topic);
}

async function updateTopoOffline(message, topic) {
  // const gatewayManager = await SDKManager.registerDevice({
  //   gateway_sn: message.from,
  //   drone_sn: null,
  //   domain: message.data.domain,
  //   type: message.data.type,
  //   sub_type: message.data.sub_type,
  //   gateway_thing_version: message.data.thing_version,
  //   drone_thing_version: null,
  // });

  // gatewayOnlineSubscribeTopic(gatewayManager);

  const device = await redisService.getDeviceOnline(message.from);
  if (!device) {
    const entity = {
      device_sn: message.from,
      sub_type: message.data.sub_type,
      type: message.data.type,
      domain: message.data.domain,
      version: message.data.version,
      control_source: null,
    };

    const gatewayDevice = await onlineSaveDevice(entity, null, null);
    if (!gatewayDevice) return null;

    // webSocketService.sendBatch(gatewayDevice?.workspace_id, null, 'device_online', {
    //   sn: null,
    //   gateway_sn: message.from
    // });

    return publish(mqttHelper.success(message), topic);
  }

  const deviceSn = device.child_device_sn;
  if (!deviceSn) {
    return publish(mqttHelper.success(message), topic);
  }

  // gatewayOnlineSubscribeTopic(SDKManager.getDeviceSDK(device.parent_sn));

  redisService.subDeviceOffline(deviceSn);

  // webSocketService.sendBatch(device.workspace_id, null, 'device_ofline', {
  //   sn: deviceSn,
  //   online_status: false,
  // });

  return publish(mqttHelper.success(message), topic);
}

async function updateTopoOnline(message, topic) {
  try {
    const updateTopoSubDevice = message.data.sub_devices[0];
    const deviceSn = updateTopoSubDevice.sn;

    const [deviceOnline, gatewayOnline] = await Promise.all([
      redisService.getDeviceOnline(deviceSn),
      redisService.getDeviceOnline(message.from),
    ]);

    const gatewayManager = await SDKManager.registerDevice({
      gateway_sn: message.from,
      drone_sn: deviceSn,
      domain: message.data.domain,
      type: message.data.type,
      sub_type: message.data.sub_type,
      gateway_thing_version: message.data.version,
      drone_thing_version: updateTopoSubDevice.version,
    });

    if (deviceOnline && gatewayOnline) {
      deviceOnlineAgain(gatewayOnline, deviceOnline);
      return publish(mqttHelper.success(message), topic);
    }

    changeSubDeviceParent(deviceSn, message.from);

    const saveDevice = {
      device_sn: message.from,
      sub_type: message.data.sub_type,
      type: message.data.type,
      domain: message.data.domain,
      version: message.data.version,
      control_source: updateTopoSubDevice?.index,
    };

    const saveSubDevice = {
      device_sn: updateTopoSubDevice.sn,
      type: updateTopoSubDevice.type,
      sub_type: updateTopoSubDevice.sub_type,
      version: updateTopoSubDevice.version,
      domain: updateTopoSubDevice.domain,
    };

    let [gateway, subDevice] = await Promise.all([
      onlineSaveDevice(saveDevice, deviceSn, null),
      onlineSaveDevice(saveSubDevice, null, message.from),
    ]);

    if (!gateway || !subDevice) return;

    gatewayOnlineSubscribeTopic(gatewayManager);

    if (!subDevice.don_vi_id) {
      return publish(mqttHelper.success(message), topic);
    }

    subDeviceOnlineSubscribeTopic(gatewayManager);

    // let deviceTopo = await manageService.getDeviceTopoForPilot(subDevice.device_sn);
    // if (!deviceTopo) {
    //   deviceTopo = {
    //     gateway_sn: gateway.device_sn,
    //   };
    // }

    // webSocketService.sendBatch(gateway.workspace_id, null, 'device_online', deviceTopo);
    return publish(mqttHelper.success(message), topic);
  } catch (error) {
    console.error('Error in updateTopoOnline:', error);
  }
}

export async function gatewayOnlineSubscribeTopic(gateway) {
  if (!gateway) return;

  subscribe(gateway);
  stateMqttService.subscribe(gateway, true);
  osdMqttService.subscribe(gateway, true);
  servicesMqttService.subscribe(gateway);
}

export function subDeviceOnlineSubscribeTopic(gateway) {
  if (!gateway) return;

  subscribe(gateway);
  stateMqttService.subscribe(gateway, false);
  osdMqttService.subscribe(gateway, false);
  servicesMqttService.subscribe(gateway);
}

async function onlineSaveDevice(device, childSn, parentSn) {
  let [deviceManage, dictionary] = await Promise.all([
    deviceService.getOne({ device_sn: device.device_sn, is_deleted: false }),
    dictionaryService.getOne({ domain: device.domain, type: device.type, sub_type: device.sub_type }),
  ]);

  let query = {
    ...device,
    child_device_sn: childSn,
    login_time: Date.now(),
  };

  if (!deviceManage) {
    query = {
      ...query,
      url_normal: ICON_URL.NORMAL_PERSON,
      url_select: ICON_URL.SELECT_PERSON,
      bound_status: false,
      ...(dictionary && {
        device_name: dictionary.device_name,
        nickname: dictionary.device_name,
        device_desc: dictionary.device_desc,
      }),
    };
    deviceManage = await deviceService.create(query);
  } else {
    deviceManage = await deviceService.findOneAndUpdate({ _id: deviceManage._id }, query);
  }

  redisService.setDeviceOnline({ ...deviceManage, status: true, parent_sn: parentSn });

  return deviceManage;
}

async function deviceOnlineAgain(gateway, device) {
  deviceService.update({ device_sn: device.device_sn }, {
    login_time: Date.now(),
  });
  deviceService.update({ device_sn: gateway.device_sn }, {
    login_time: Date.now(),
    child_device_sn: device.device_sn,
  });

  gateway.child_device_sn = device.device_sn;
  device.parent_sn = gateway.device_sn;

  redisService.setDeviceOnline(gateway);
  redisService.setDeviceOnline(device);

  if (device.don_vi_id) {
    subDeviceOnlineSubscribeTopic(SDKManager.getDeviceSDK(gateway.device_sn));
  }
}

async function changeSubDeviceParent(deviceSn, gatewaySn) {
  const devices = await deviceService.getAll({
    child_device_sn: deviceSn,
    is_deleted: false,
    device_sn: { $ne: gatewaySn },
  });

  devices.map(async dv => {
    const device = await deviceService.update({ _id: dv._id }, {
      child_device_sn: null,
    });

    const online = await redisService.getDeviceOnline(dv.device_sn);
    if (online) {
      online.child_device_sn = null;
      await redisService.setDeviceOnline(online);
    }
  });
}
