import * as mqttService from '../mqtt.service';
import * as redisService from '../../Redis/redis.service';
import { OSD_DEVICE_TYPE } from '../../../../constant/constant';
// import * as webSocketService from '../../WebSocket/webSocket.service';
import * as SDKManager from '../../common/SDKManager';
import * as manageService from '../../common/SDKManager';

export async function subscribe(gateway, unsubscribeSubDevice) {
  SDKManager.registerDevice(gateway);

  mqttService.subscribe(`thing/product/${gateway.gateway_sn}/osd`);
  if (unsubscribeSubDevice) {
    mqttService.unsubscribe(`thing/product/${gateway.drone_sn}/osd`);
    return;
  }
  if (gateway?.drone_sn) {
    mqttService.subscribe(`thing/product/${gateway.drone_sn}/osd`);
  }
}

export async function unsubscribe(gateway) {
  // SDKManager.logoutDevice(gateway.gateway_sn);
  mqttService.unsubscribe(`thing/product/${gateway.gateway_sn}/osd`);
  if (gateway?.drone_sn) {
    mqttService.unsubscribe(`thing/product/${gateway.drone_sn}/osd`);
  }
}

export function osdRouterFlow(message, topic) {
  message.from = topic.substring(topic.indexOf('thing/product/') + 'thing/product/'.length, topic.indexOf('/osd'));

  const gateway = SDKManager.getDeviceSDK(message.gateway);
  const typeEnum = OSD_DEVICE_TYPE.find(gateway.gateway_type, message.from === message.gateway);

  switch (typeEnum) {
    case OSD_DEVICE_TYPE.RC:
      return osdRemoteControl(message, topic);
    case OSD_DEVICE_TYPE.DOCK:
      return osdDock(message, topic);
    case OSD_DEVICE_TYPE.RC_DRONE:
      return osdRcDrone(message, topic);
    case OSD_DEVICE_TYPE.DOCK_DRONE:
      return osdDockDrone(message, topic);
  }
}

async function osdRemoteControl(message, topic) {
  const from = message.from;
  const device = await checkDeviceOnline(from);
  if (!device) return;

  if (device.child_device_sn) {
    const childDevice = await manageService.getDeviceBySn(device.child_device_sn);
    if (childDevice) {
      device.children = childDevice;
    }
  }

  redisService.setDeviceOnline(device);

  // webSocketService.sendBatch(device.workspace_id, USER_TYPE.PILOT, 'device_osd', {
  //   sn: from,
  //   host: {
  //     latitude: message.data.latitude,
  //     longitude: message.data.longitude,
  //     height: message.data.height,
  //   }
  // });
  // webSocketService.sendBatch(device.workspace_id, USER_TYPE.WEB, 'gateway_osd', {
  //   sn: from,
  //   host: message.data
  // });
}

async function osdDock(message, topic) {
  console.log('osdDock not implemented');
}

async function osdRcDrone(message, topic) {
  const from = message.from;
  const device = await checkDeviceOnline(from);
  if (!device) return;

  redisService.setDeviceOnline(device);

  // webSocketService.sendBatch(device.workspace_id, USER_TYPE.PILOT, 'device_osd', {
  //   sn: from,
  //   host: message.data
  // });
  // webSocketService.sendBatch(device.workspace_id, USER_TYPE.WEB, 'device_osd', {
  //   sn: from,
  //   host: message.data
  // });
}

async function osdDockDrone(message, topic) {
  console.log('osdDockDrone not implemented');
}

async function checkDeviceOnline(sn) {
  let device = await redisService.getDeviceOnline(sn);
  if (!device) {
    device = await manageService.getDeviceBySn(sn);
    if (!device) return;
  }

  return device;
}
