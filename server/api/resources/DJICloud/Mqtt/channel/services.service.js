import * as mqttService from '../mqtt.service';
import * as uuid from 'uuid';
import Chan from '../chan';

export async function publish(sn, method, data = {}, timeout = 3000, bid = null, retryCount = 2) {
  const topic = `thing/product/${sn}/services`;

  const request = {
    gateway_sn: sn,
    tid: uuid.v4(),
    bid: bid || uuid.v4(),
    timestamp: Date.now(),
    method: method,
    data: data,
  };

  const response = await mqttService.publishWithReply(topic, request, retryCount, timeout);
  const replyReceiver = response.data;

  return {
    ...response,
    data: {
      result: replyReceiver.result,
      output: replyReceiver.output || replyReceiver.info || '',
    },
  };
}

export async function subscribe(gateway) {
  mqttService.subscribe(`thing/product/${gateway.gateway_sn}/services_reply`);
}

export async function unsubscribe(gateway) {
  mqttService.unsubscribe(`thing/product/${gateway.gateway_sn}/services_reply`);
}

export function servicesReply(message, topic) {
  const chan = Chan.getInstance(message.tid, false);
  if (!chan) return;

  if (message.method === 'fileupload_list') {
    message.data.output = message.data;
  }

  chan.put(message);
}
