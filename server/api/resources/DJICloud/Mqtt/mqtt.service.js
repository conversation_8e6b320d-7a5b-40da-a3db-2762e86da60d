import mqtt from 'mqtt';
import { getConfig } from '../../../../config/config';
import { CLOUD_API_TOPIC } from '../../../constant/constant';
import { statusRouterFlow } from './channel/status.service';
import { stateDataRouterFlow } from './channel/state.service';
import { servicesReply } from './channel/services.service';
import { osdRouterFlow } from './channel/osd.service';
import * as uuid from 'uuid';
import Chan from './chan';

const config = getConfig(process.env.NODE_ENV);

let client;
let subscribedTopics = new Set();

export const mqttConnect = (username, password) => {
  const connectUrl = getMqttAddress();

  client = mqtt.connect(connectUrl, {
    clientId: `mqtt_${Math.random().toString(16).slice(3)}`,
    clean: true,
    connectTimeout: 4000,
    username: username || `${config.mqtt.username}`,
    password: password || `${config.mqtt.password}`,
    reconnectPeriod: 1000,
  });

  client
    .on('connect', () => {
      console.log('Successfully connected to Mqtt');
      const initialTopics = config.mqtt.topics.split(',');

      client.subscribe(initialTopics, (err, granted) => {
        if (err) console.error('Subscription error:', err);
        granted.forEach(sub => {
          subscribedTopics.add(sub.topic);
        });
      });
    })
    .on('error', (err) => {
      console.error('Connection error: ', err);
    })
    .on('close', () => {
      console.log('Connection MQTT closed');
    });

  client.on('message', (topic, message) => determineTargetChannels(topic, message));
};

function getMqttAddress() {
  const options = config.mqtt;
  return `${options.protocol}://${options.host.trim()}:${options.port}`;
}

export function publish(topic, qos = 0, message, publishCount = 1) {
  const payload = JSON.stringify(message);
  let time = 0;

  while (time++ < publishCount) {
    client.publish(topic, payload, { qos }, (err) => {
      if (err) console.log('Publish error:', err);
    });
  }
}

export function publishReply(message, topic) {
  publish(topic + '_reply', 2, message);
}

export async function publishWithReply(topic, message, retryCount, timeout) {
  let time = 0;
  const hasBid = message?.bid;
  message.bid = hasBid ? message.bid : uuid.v4();

  while (time++ <= retryCount) {
    publish(topic, 0, message);

    const receiver = await Chan.getInstance(message.tid, true).get(message.tid, timeout);

    if (receiver && receiver.tid === message.tid && receiver.bid === message.bid) {
      return receiver;
    }

    if (!hasBid) {
      message.bid = uuid.v4();
    }
    message.tid = uuid.v4();
  }
  throw new Error('No message reply received.');
}

export function subscribe(topic) {
  if (subscribedTopics.has(topic)) return;

  client.subscribe(topic, { qos: 1 }, (err, granted) => {
    if (err) console.log('Subscription error:', err);
    subscribedTopics.add(topic);
  });
}

export function unsubscribe(topic) {
  client.unsubscribe(topic, (err) => {
    if (err) console.log('Unsubscribe error:', err);
  });
}

export function getSubscribedTopic() {
  return subscribedTopics;
}

function determineTargetChannels(topic, message) {
  const topicEnum = CLOUD_API_TOPIC.find(topic);

  const payload = JSON.parse(message.toString());

  switch (topicEnum) {
    case CLOUD_API_TOPIC.STATUS:
      return statusRouterFlow(payload, topic);
    case CLOUD_API_TOPIC.STATE:
      return stateDataRouterFlow(payload, topic);
    case CLOUD_API_TOPIC.SERVICE_REPLY:
      return servicesReply(payload, topic);
    case CLOUD_API_TOPIC.OSD:
      return osdRouterFlow(payload, topic);
    default:
      break;
  }
}
