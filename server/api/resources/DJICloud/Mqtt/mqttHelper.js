export function success(message, data) {
  return {
    tid: message.tid,
    bid: message.bid,
    method: message.method,
    timestamp: message.timestamp,
    data: {
      result: 0,
      ...(data && { output: data }),
    },
  };
}

export function error(message, error) {
  return {
    tid: message.tid,
    bid: message.bid,
    method: message.method,
    timestamp: message.timestamp,
    data: {
      result: error?.status_code || -1,
      output: error?.message || 'failed',
    },
  };
}
