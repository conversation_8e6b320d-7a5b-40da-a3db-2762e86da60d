import { createClient } from 'redis';
import { getConfig } from '../../../../config/config';
import { subDeviceOnlineSubscribeTopic } from '../Mqtt/channel/status.service';
import * as SDKManager from '../common/SDKManager';

const { redis } = getConfig(process.env.NODE_ENV);

let redisClient = null;

export const redisConnect = () => {
  redisClient = createClient({ url: redis });

  redisClient.connect().then(async () => {
    console.log('Successfully connected to Redis');
    await afterConnect();
  }).catch(err => console.log('Could not connect to Redis: ', err));

  redisClient.on('error', (err) => console.error('Redis Client Error', err));
  redisClient.on('disconnect', () => console.log('Redis client disconnected'));

  return redisClient;
};

async function afterConnect() {
  const keys = await redisClient.keys('online:*');
  const deviceSns = keys.map(key => key.substring('online:'.length));

  await Promise.all(deviceSns.map(async (deviceSn) => {
    const device = await getDeviceOnline(deviceSn);
    if (!device) return;

    const childDevice = await getDeviceOnline(device?.child_device_sn);

    const gateway = await SDKManager.registerDevice({
      gateway_sn: device?.device_sn,
      drone_sn: device?.child_device_sn,
      domain: device?.domain,
      type: device?.type,
      sub_type: device?.sub_type,
      gateway_version: device?.version,
      drone_version: childDevice?.version,
    });

    subDeviceOnlineSubscribeTopic(gateway);
  }));
}

export async function hashSet(key, field, value) {
  return redisClient.hSet(key, field, JSON.stringify(value));
}

export async function hashGet(key, field) {
  const value = await redisClient.hGet(key, field);
  return value ? JSON.parse(value) : null;
}

export async function hashKeys(key) {
  return redisClient.hKeys(key);
}

export async function hashCheck(key, field) {
  return redisClient.hExists(key, field);
}

export async function hashDel(key, fields) {
  const result = await redisClient.hDel(key, fields);
  return result > 0;
}

export async function hashLen(key) {
  return redisClient.hLen(key);
}

export async function expireKey(key, timeout) {
  return redisClient.expire(key, timeout);
}

export async function set(key, value) {
  return await redisClient.set(key, JSON.stringify(value));
}

export async function get(key) {
  const value = await redisClient.get(key);
  return value ? JSON.parse(value) : null;
}

export async function setWithExpire(key, value, expire) {
  return redisClient.setEx(key, expire, JSON.stringify(value));
}

export async function getExpire(key) {
  return redisClient.ttl(key);
}

export async function checkExist(key) {
  return await redisClient.exists(key);
}

export async function del(key) {
  const exists = await checkExist(key);
  const deleteCount = await redisClient.del(key);
  return exists > 0 && deleteCount > 0;
}

export async function getAllKeys(pattern) {
  return redisClient.keys(pattern);
}

export async function listRPush(key, values) {
  if (values.length === 0) return;
  return redisClient.rPush(key, values.map(v => JSON.stringify(v)));
}

export async function listGet(key, start, end) {
  const values = await redisClient.lRange(key, start, end);
  return values.map(v => JSON.parse(v));
}

export async function listGetAll(key) {
  return listGet(key, 0, -1);
}

export async function listLen(key) {
  return redisClient.lLen(key);
}

export async function zAdd(key, value, score) {
  const add = await redisClient.zAdd(key, { score, value: JSON.stringify(value) });
  return add > 0;
}

export async function zRemove(key, value) {
  const remove = await redisClient.zRem(key, JSON.stringify(value));
  return remove > 0;
}

export async function zRange(key, start, end) {
  const values = await redisClient.zRange(key, start, end);
  return values.map(v => JSON.parse(v));
}

export async function zGetMin(key) {
  const values = await zRange(key, 0, 0);
  return values.length > 0 ? values[0] : null;
}

export async function zScore(key, value) {
  return redisClient.zScore(key, JSON.stringify(value));
}

export async function zIncrement(key, value, delta) {
  return redisClient.zIncrBy(key, delta, JSON.stringify(value));
}

// Redis operations for wayline jobs
export async function setRunningWaylineJob(dockSn, data) {
  return setWithExpire(`wayline_job_running:${dockSn}`, data, 3600);
}

export async function getRunningWaylineJob(dockSn) {
  return get(`wayline_job_running:${dockSn}`);
}

export async function delRunningWaylineJob(dockSn) {
  return del(`wayline_job_running:${dockSn}`);
}

export async function setPausedWaylineJob(dockSn, jobId) {
  await setWithExpire(`wayline_job_paused:${dockSn}`, jobId, 3600);
}

export async function getPausedWaylineJobId(dockSn) {
  return get(`wayline_job_paused:${dockSn}`);
}

export async function delPausedWaylineJob(dockSn) {
  return del(`wayline_job_paused:${dockSn}`);
}

export async function setBlockedWaylineJob(dockSn, jobId) {
  await setWithExpire(`wayline_job_block:${dockSn}`, jobId, 600);
}

export async function getBlockedWaylineJobId(dockSn) {
  return get(`wayline_job_block:${dockSn}`);
}

export async function setConditionalWaylineJob(waylineJob) {
  const expireTime = Math.floor((new Date(waylineJob.end_time) - new Date()) / 1000);
  if (expireTime <= 0) {
    return false; // Job đã hết hạn
  }
  return await setWithExpire(`wayline_job_condition_prepare:${waylineJob._id}`, waylineJob, expireTime);
}

export async function getConditionalWaylineJob(jobId) {
  return get(`wayline_job_condition_prepare:${jobId}`);
}

export async function delConditionalWaylineJob(jobId) {
  return del(`wayline_job_condition_prepare:${jobId}`);
}

export async function addPrepareConditionalWaylineJob(waylineJob) {
  if (!waylineJob?.begin_time) return false;
  return zAdd('wayline_job_condition_prepare', `${waylineJob.workspace_id}:${waylineJob.dock_sn}:${waylineJob._id}`, new Date(waylineJob.begin_time).getTime());
}

export async function getNearestConditionalWaylineJob() {
  return zGetMin('wayline_job_condition_prepare');
}

export async function getConditionalWaylineJobTime(jobKey) {
  return zScore('wayline_job_condition_prepare', jobKey);
}

export async function removePrepareConditionalWaylineJob(jobKey) {
  return zRemove('wayline_job_condition_prepare', jobKey);
}

// Redis operations for media files
export async function setMediaCount(gatewaySn, jobId, mediaFile) {
  await hashSet(`media_file:${gatewaySn}`, jobId, mediaFile);
}

export async function getMediaCount(gatewaySn, jobId) {
  return hashGet(`media_file:${gatewaySn}`, jobId);
}

export async function delMediaCount(gatewaySn, jobId) {
  return hashDel(`media_file:${gatewaySn}`, [jobId]);
}

export async function detMediaCountByDeviceSn(gatewaySn) {
  return del(`media_file:${gatewaySn}`);
}

export async function setMediaHighestPriority(gatewaySn, mediaFile) {
  return setWithExpire(`media_highest_priority:${gatewaySn}`, mediaFile, 300);
}

export async function getMediaHighestPriority(gatewaySn) {
  return get(`media_highest_priority:${gatewaySn}`);
}

export async function delMediaHighestPriority(gatewaySn) {
  return del(`media_highest_priority:${gatewaySn}`);
}

// Redis operations for device online status
export async function checkDeviceOnline(sn) {
  const key = `online:${sn}`;
  const exists = await checkExist(key);
  const expire = await getExpire(key);
  return exists > 0 && expire > 0;
}

export async function getDeviceOnline(sn) {
  return await get(`online:${sn}`);
}

export async function setDeviceOnline(device) {
  return await setWithExpire(`online:${device.device_sn}`, device, 60);
}

export async function delDeviceOnline(sn) {
  return del(`online:${sn}`);
}

export async function setDeviceOsd(sn, data) {
  await setWithExpire(`osd:${sn}`, data, 60);
}

export async function getDeviceOsd(sn) {
  return get(`osd:${sn}`);
}

export async function delDeviceOsd(sn) {
  return del(`osd:${sn}`);
}

// Redis operations for firmware upgrading
export async function setFirmwareUpgrading(sn, events) {
  await setWithExpire(`upgrading:${sn}`, events, 60 * 20);
}

export async function getFirmwareUpgradingProgress(sn) {
  return get(`upgrading:${sn}`);
}

export async function delFirmwareUpgrading(sn) {
  return del(`upgrading:${sn}`);
}

// Redis operations for HMS keys
export async function addEndHmsKeys(sn, keys) {
  await listRPush(`hms:${sn}`, keys);
}

export async function getAllHmsKeys(sn) {
  return listGetAll(`hms:${sn}`);
}

export async function delHmsKeysBySn(sn) {
  return del(`hms:${sn}`);
}

export async function gatewayOffline(gatewaySn) {
  await delDeviceOnline(gatewaySn);
  await delHmsKeysBySn(gatewaySn);
  await deleteCapacityCameraByDeviceSn(gatewaySn);
}

export async function subDeviceOffline(deviceSn) {
  await delDeviceOnline(deviceSn);
  await delDeviceOsd(deviceSn);
  await delHmsKeysBySn(deviceSn);
  await deleteCapacityCameraByDeviceSn(deviceSn);
}

export async function getCapacityCameraByDeviceSn(deviceSn) {
  return hashGet('live_capacity', deviceSn);
}

export async function deleteCapacityCameraByDeviceSn(deviceSn) {
  return hashDel('live_capacity', [deviceSn]);
}

export async function setDrcModeInRedis(dockSn, clientId) {
  return setWithExpire(`drc:${dockSn}`, clientId, 3600);
}

export async function getDrcModeInRedis(dockSn) {
  return get(`drc:${dockSn}`);
}

export async function delDrcModeInRedis(dockSn) {
  return del(`drc:${dockSn}`);
}
