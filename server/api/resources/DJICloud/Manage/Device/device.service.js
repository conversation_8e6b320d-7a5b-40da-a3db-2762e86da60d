import Model from './device.model';
import * as ValidatorHelper from '../../../../helpers/validatorHelper';

const joi = require('joi');
const objSchema = joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return Model.find(query).lean();
}

export async function getAllPaginate(criteria, options) {
  return Model.paginate(criteria, options);
}

export async function getOne(query) {
  return Model.findOne(query).lean();
}

export async function update(query, update) {
  return Model.updateMany(query, update, { new: true });
}

export async function create(query) {
  return Model.create(query);
}

export async function findOneAndUpdate(query, update) {
  return Model.findOneAndUpdate(query, update, { new: true }).lean();
}

export async function remove(query) {
  return Model.updateMany(query, { is_deleted: true });
}
