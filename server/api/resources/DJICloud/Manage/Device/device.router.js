import express from 'express';
import * as Controller from './device.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../../logs/middleware';

export const deviceRouter = express.Router();

deviceRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

deviceRouter
  .route('/')
  .post(Controller.create)
  .get(Controller.getAll);

deviceRouter
  .route('/:id')
  .get(Controller.findOne)
  .put(Controller.update)
  .delete(Controller.remove);
