import Model from './device.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as Service from './device.service';

const searchLikes = ['device_name'];
const populateOpts = [];
const sortOpts = { created_at: -1 };

export const getAll = controllerHelper.createGetAllFunction(Model, searchLikes, populateOpts, sortOpts, 'don_vi_id');
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
