import mongoose, { Schema } from 'mongoose';
import { DJI_DEVICE, DON_VI, USER } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  device_sn: { type: String },
  device_name: { type: String },
  type: { type: Number, default: -1 },
  sub_type: { type: Number, default: -1 },
  domain: { type: Number, default: -1 },
  device_desc: { type: String },
  user_id: { type: Schema.Types.ObjectId, ref: USER },
  nickname: { type: String },
  don_vi_id: { type: Schema.Types.ObjectId, ref: DON_VI },
  firmware_version: { type: String },
  compatible_status: { type: Boolean, default: true },
  version: { type: String },
  control_source: { type: String },
  child_device_sn: { type: String },
  bound_time: { type: Date },
  bound_status: { type: <PERSON>olean, default: false },
  login_time: { type: Date, default: Date.now },
  url_normal: { type: String },
  url_select: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});


schema.plugin(mongoosePaginate);
export default mongoose.model(DJI_DEVICE, schema, DJI_DEVICE);
