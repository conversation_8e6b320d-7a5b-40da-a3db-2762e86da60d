import * as deviceService from './Device/device.service';
import * as redisService from '../Redis/redis.service';
import * as statusMqttService from '../Mqtt/channel/status.service';
import * as djiResponseHelper from '../../../helpers/djiResponseHelper';
import * as SDKManager from '../common/SDKManager';
import { DEVICE_DOMAIN, DEVICE_FIRMWARE_STATUS } from '../../../constant/constant';

export async function bindDevice(req, res) {
  try {
    const param = req.body;
    const { don_vi_id } = req.user;

    const device = await redisService.getDeviceOnline(param.device_sn);
    if (!device) {
      return responseHelper.error(res, 'Device is offline.');
    }

    const firmwareStatus = param?.firmware_status;
    await deviceService.update({ device_sn: param.device_sn }, {
      ...param,
      ...(firmwareStatus && { compatible_status: DEVICE_FIRMWARE_STATUS.CONSISTENT_UPGRADE !== firmwareStatus }),
      bound_status: true,
      don_vi_id: don_vi_id,
      bound_time: Date.now(),
    });

    const redisDevice = {
      ...device,
      don_vi_id: don_vi_id,
    };

    await redisService.setDeviceOnline(redisDevice);

    let gatewaySn, deviceSn;
    if (DEVICE_DOMAIN.REMOTER_CONTROL === redisDevice.domain) {
      gatewaySn = param.device_sn;
      deviceSn = redisDevice.child_device_sn;
    } else {
      gatewaySn = redisDevice.parent_sn;
      deviceSn = param.device_sn;
    }

    // const deviceTopo = await manageService.getDeviceTopoForPilot(deviceSn);
    // const websocketData = deviceTopo || {
    //   sn: deviceSn,
    //   gateway_sn: gatewaySn,
    // };
    // webSocketService.sendBatch(param.workspace_id, null, 'device_online', websocketData);

    statusMqttService.subDeviceOnlineSubscribeTopic(SDKManager.getDeviceSDK(gatewaySn));

    return djiResponseHelper.success(res);
  } catch (error) {
    return djiResponseHelper.error(res, error);
  }
}
