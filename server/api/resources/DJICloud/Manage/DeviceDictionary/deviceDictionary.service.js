import Model from './deviceDictionary.model';

import * as ValidatorHelper from '../../../../helpers/validatorHelper';

const joi = require('joi');
const objSchema = joi.object({});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getOne(query) {
  return Model.findOne(query).lean();
}
