import Model from './deviceDictionary.model';
import * as controllerHelper from '../../../../helpers/controllerHelper';
import * as Service from './deviceDictionary.service';

const searchLikes = ['device_name'];
const populateOpts = [];
const sortOpts = { created_at: -1 };

export const getAll = controllerHelper.createGetAllFunction(Model, searchLikes, populateOpts, sortOpts);
export const findOne = controllerHelper.createFindOneFunction(Model, populateOpts);
export const update = controllerHelper.createUpdateByIdFunction(Model, Service, populateOpts);
export const create = controllerHelper.createCreateFunction(Model, Service, populateOpts);
export const remove = controllerHelper.createRemoveFunction(Model);
