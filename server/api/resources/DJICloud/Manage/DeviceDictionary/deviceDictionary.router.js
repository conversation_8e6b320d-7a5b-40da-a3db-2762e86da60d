import express from 'express';
import * as Controller from './deviceDictionary.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../../logs/middleware';

export const deviceDictionaryRouter = express.Router();

deviceDictionaryRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

deviceDictionaryRouter
  .route('/')
  .post(Controller.create)
  .get(Controller.getAll);

deviceDictionaryRouter
  .route('/:id')
  .get(Controller.findOne)
  .put(Controller.update)
  .delete(Controller.remove);
