import mongoose, { Schema } from 'mongoose';
import { DJI_DEVICE_DICTIONARY } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  domain: { type: Number },
  type: { type: Number },
  sub_type: { type: Number },
  device_name: { type: String },
  device_desc: { type: String },
  code: { type: String },
  is_deleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);

export default mongoose.model(DJI_DEVICE_DICTIONARY, schema, DJI_DEVICE_DICTIONARY);
