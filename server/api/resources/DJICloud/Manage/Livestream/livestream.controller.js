import * as deviceService from '../../Manage/Device/device.service';
import * as redisService from '../../Redis/redis.service';
import * as livestreamService from './livestream.service';
import * as servicesMqttService from '../../Mqtt/channel/services.service';
import * as responseHelper from '../../../../helpers/responseHelper';
import { DEVICE_DOMAIN } from '../../../../constant/constant';
import CommonError from '../../../../error/CommonError';
import * as SDKManager from '../../common/SDKManager';
import { getConfig } from '../../../../../config/config';
import queryHelper from '../../../../helpers/queryHelper';

const config = getConfig(process.env.NODE_ENV);

export async function getLiveCapacity(req, res) {
  try {
    // const { don_vi_id } = req.user;
    let query = queryHelper.extractQueryParam(req, ['device_name', 'device_sn']);
    const { criteria, options } = query;

    // criteria.don_vi_id = don_vi_id;
    criteria.domain = { $in: [DEVICE_DOMAIN.DRONE, DEVICE_DOMAIN.DOCK] };
    options.sort = { login_time: 1 };

    const keys = await redisService.getAllKeys('online:*');
    const deviceSns = keys.map(key => key.substring('online:'.length));
    criteria.device_sn = { $in: deviceSns };

    const paginate = await deviceService.getAllPaginate(criteria, options);

    const devicePromises = paginate.docs.map(async dv => {
      return {
        device_name: dv.device_name,
        device_sn: dv.device_sn,
        online: true,
        cameras: await redisService.hashGet('live_capacity', dv.device_sn),
      };
    });

    paginate.docs = await Promise.all(devicePromises);

    return responseHelper.success(res, paginate);
  } catch (err) {
    return responseHelper.error(res, err);
  }
}

export async function liveStart(req, res) {
  try {
    const { error, value } = livestreamService.validate(req.body, 'POST');
    console.log(error, value);
    if (error) return responseHelper.error(res, error, 400);
    const { video_id, video_quality } = value;

    const device = await livestreamService.checkBeforeLive(video_id);
    const { device_sn } = device;
    const url = config.rtmp + device_sn.toLocaleString();

    const deviceManager = SDKManager.getDeviceSDK(device_sn);
    if (!deviceManager) {
      return responseHelper.error(res, CommonError.DRONE_NOT_FOUND);
    }

    // const reply = await servicesMqttService.publish(device_sn, 'live_start_push', { url, url_type: 1, video_id, video_quality }, 20000);
    // if (reply.data.result !== 0) {
    //   return responseHelper.error(res, reply);
    // }

    const live = url.replace('rtmp', 'https').replace('live', 'hls') + '/index.m3u8';

    return responseHelper.success(res, { url: live });
  } catch (error) {
    return responseHelper.error(res, error);
  }
}

export async function liveStop(req, res) {
  try {
    const { video_id } = req.body;
    const device = await livestreamService.checkBeforeLive(video_id);
    const { device_sn } = device;

    const deviceManager = await SDKManager.getDeviceSDK(device_sn);
    if (!deviceManager) {
      return responseHelper.error(res, CommonError.DRONE_NOT_FOUND);
    }

    const reply = await servicesMqttService.publish(device_sn, 'live_stop_push', { video_id, }, 20000, null, 2);
    if (reply.data.result !== 0) {
      return responseHelper.error(res, reply);
    }

    return responseHelper.success(res, {});
  } catch (error) {
    return responseHelper.error(res, error);
  }
}

export async function liveSetQuality(req, res) {
  try {
    const { video_id, video_quality } = req.body;



    const device = await livestreamService.checkBeforeLive(video_id);
    const { device_sn } = device;

    const deviceManager = await SDKManager.getDeviceSDK(device_sn);
    if (!deviceManager) {
      return responseHelper.error(res, CommonError.DRONE_NOT_FOUND);
    }

    const reply = await servicesMqttService.publish(deviceSn, 'live_set_quality', { video_quality, video_id }, 20000, null, 2);
    if (reply.data.result !== 0) {
      return responseHelper.error(res, reply);
    }

    return responseHelper.success(res, {});
  } catch (error) {
    return responseHelper.error(res, error);
  }
}

export async function liveLensChange(req, res) {
  try {
    const { video_id, video_type } = req.body;
    const device = await livestreamService.checkBeforeLive(video_id);
    const { device_sn } = device;

    const deviceManager = await SDKManager.getDeviceSDK(device_sn);
    if (!deviceManager) {
      return responseHelper.error(res, CommonError.DRONE_NOT_FOUND);
    }

    const reply = await servicesMqttService.publish(device_sn, 'live_lens_change', { video_type, video_id }, null, 2, 20000);
    if (reply.data.result !== 0) {
      return responseHelper.error(res, reply);
    }

    return responseHelper.success(res, {});
  } catch (error) {
    return responseHelper.error(res, error);
  }
}
