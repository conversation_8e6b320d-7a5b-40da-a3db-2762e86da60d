import * as deviceService from '../../Manage/Device/device.service';
import * as deviceDictionaryService from '../../Manage/DeviceDictionary/deviceDictionary.service';
import * as redisService from '../../Redis/redis.service';
import CommonError from '../../../../error/CommonError';
import { DEVICE_DOMAIN } from '../../../../constant/constant';
import * as uuid from 'uuid';

import * as ValidatorHelper from '../../../../helpers/validatorHelper';
const joi = require('joi');
const objSchema = joi.object({
  video_id: joi.string().required().messages(ValidatorHelper.messageDefine('video_id')),
  video_quality: joi.string().required().messages(ValidatorHelper.messageDefine('video_quality')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function saveLiveCapacity(devices) {
  devices.map(async dv => {

    const { sn, camera_list } = dv;
    const capacity = await Promise.all(camera_list.map(async camera => {
      const cameraIndex = camera.camera_index;
      const [type, sub_type, domain] = cameraIndex.split('-');

      const dictionary = await deviceDictionaryService.getOne({
        domain: DEVICE_DOMAIN.PAYLOAD,
        type: type,
        sub_type: sub_type,
      });

      const videos = camera.video_list.map(video => {
        const { video_index, video_type, switch_able_video_types } = video;

        const switchAbleVideoTypes = switch_able_video_types
          ? video.switch_able_video_types.map(vt => vt.type)
          : null;

        return {
          video_index: video_index,
          video_type: video_type,
          switch_able_video_types: switchAbleVideoTypes,
        };
      });

      return {
        ...(dictionary && { name: dictionary.device_name }),
        id: uuid.v4(),
        index: cameraIndex,
        video_list: videos,
      };
    }));

    redisService.hashSet('live_capacity', sn, capacity);
  });
}

export async function checkBeforeLive(videoId) {
  const [droneSn] = videoId.split('/');
  const device = await deviceService.getOne({ device_sn: droneSn });
  if (!device) throw CommonError.DRONE_NOT_FOUND;

  const online = await redisService.checkDeviceOnline(droneSn);
  if (DEVICE_DOMAIN.DOCK === device.domain) {
    return { ...device, online };
  }

  const gateway = await deviceService.getOne({ child_device_sn: droneSn });
  if (!gateway) throw CommonError.DRONE_NOT_FOUND;
  return gateway;
}
