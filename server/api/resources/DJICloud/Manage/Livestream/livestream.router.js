import express from 'express';
import * as Controller from './livestream.controller';
import passport from 'passport';
import { loggerMiddleware } from '../../../../logs/middleware';

export const livestreamRouter = express.Router();

livestreamRouter.use(passport.authenticate('jwt', { session: false }), loggerMiddleware);

livestreamRouter
  .route('/capacity')
  .get(Controller.getLiveCapacity);
livestreamRouter
  .route('/start')
  .post(Controller.liveStart);
livestreamRouter
  .route('/stop')
  .post(Controller.liveStop);
livestreamRouter
  .route('/update')
  .post(Controller.liveSetQuality);
livestreamRouter
  .route('/switch')
  .post(Controller.liveLensChange);
