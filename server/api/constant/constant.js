export const USER_CODES = {
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
};

export const ROLE_CODES = {
  CODE_SYSTEM_ADMIN: 'CODE_SYSTEM_ADMIN',
};

export const CAP_DON_VI = {
  TONG_CONG_TY: 'TONG_CONG_TY',
  CONG_TY: 'CONG_TY',
  TRUYEN_TAI_DIEN: 'TRUYEN_TAI_DIEN',
  DOI_TRUYEN_TAI_DIEN: 'DOI_TRUYEN_TAI_DIEN',
};

export const TYPE_DON_VI = {
  TRIAL: 'TRIAL',
  STANDARD: 'STANDARD',
  PRO: 'PRO',
};

export const STORE_DIRS = {
  AVATAR: './storage/avatars',
  CHU_KY: './storage/chu_kys',
  MOBILE_APP: './storage/mobile_app',
  HO_SO_THIET_BI: './storage/ho_so_thiet_bi',
  ANH_VI_TRI: './storage',
  LOGGER_DATA: './storage/logs',
  PHIEU_GIAO_VIEC: './storage/phieugiaoviec',
  IMAGE: './storage/image',
  STORAGE: './storage',
  S3_SERVER_DATA: './storage/s3_server_data',
  DRONE_WAYLINE: './storage/drone_wayline',
  DRONE_MEDIA: './storage/drone_media',
};

export const TEMPLATES_DIRS = {
  TEMPLATES: './server/templates',
  BIEU_MAU: './server/templates/bieumau',
  REPORT: './server/templates/reports',
  CONG_TRINH_XAY_DUNG: './server/templates/congtrinhxaydung',
  DIGITAL_SIGN: './server/templates/digitalSignFile',
  FILE_IMPORT_SAMPLE: './server/templates/fileImportSample',
  OUTPUT_FILE: './server/templates/outputFile',
  PHIEU_DO_THONG_SO: './server/templates/phieudothongso',
  PDF: './server/templates/pdf',
  TONG_KE: './server/templates/tongke',
  LY_LICH_VAN_HANH: './server/templates/lylichvanhanh',
  KHOILUONGQUANLY: './server/templates/khoiluongquanly',
  PHIEUGIAOVIEC: './server/templates/phieugiaoviec',
  RA_SOAT_DU_LIEU: './server/templates/raSoatDuLieu',
};

export const CAP_HO_SO = {
  CAP_1: 'CAP_1',
  CAP_2: 'CAP_2',
  CAP_3: 'CAP_3',
};

export const TEXT_TO_FIND = {
  PHIEU_GIAO_VIEC: 'ký, ghi rõ họ và tên',
  PHIEU_KT_KH: 'ký và ghi rõ họ tên',
  PHIEU_CONG_TAC: 'chữ ký',
};

export const TINH_TRANG_PHIEU = {
  DANG_GIAO_CHAM: { code: 'DANG_GIAO_CHAM', label: 'Đang giao chậm' },
  DA_GIAO_CHAM: { code: 'DA_GIAO_CHAM', label: 'Đã giao chậm' },
  DANG_TIEP_NHAN_CHAM: { code: 'DANG_TIEP_NHAN_CHAM', label: 'Đang tiếp nhận chậm' },
  DA_TIEP_NHAN_CHAM: { code: 'DA_TIEP_NHAN_CHAM', label: 'Đã tiếp nhận chậm' },
  DANG_THUC_HIEN_CHAM: { code: 'DANG_THUC_HIEN_CHAM', label: 'Đang thực hiện chậm' },
  DA_THUC_HIEN_CHAM: { code: 'DA_THUC_HIEN_CHAM', label: 'Đã thực hiện chậm' },
  DANG_XAC_NHAN_KHOA_CHAM: { code: 'DANG_XAC_NHAN_KHOA_CHAM', label: 'Đang xác nhận khóa chậm' },
  DA_XAC_NHAN_KHOA_CHAM: { code: 'DA_XAC_NHAN_KHOA_CHAM', label: 'Đã xác nhận khóa chậm' },
};

export const KET_LUAN = {
  DAT: { label: 'Đạt', value: 'DAT' },
  KHONG_DAT: { label: 'Không đạt', value: 'KHONG_DAT' },
};

export const HUONG_DO = {
  PHIA_NHO: 'Phía cột thứ tự nhỏ',
  PHIA_LON: 'Phía cột thứ tự lớn',
};

export const PDF_WIDTH = 595.273;
export const PDF_HEIGHT = 841.886;

export const EXTENSION_FILE = {
  DOCX: 'docx',
  PDF: 'pdf',
  XLSX: 'xlsx',
};

export const LOAI_DAY = {
  DAY_CAP_QUANG: { label: 'Dây cáp quang', code: 'DAY_CAP_QUANG' },
  DAY_CHONG_SET: { label: 'Dây chống sét', code: 'DAY_CHONG_SET' },
};

export const USER_NAME_ADDON = '1npt\\';

export const GENDER_OPTIONS = {
  MALE: { label: 'Nam', value: 'MALE' },
  FEMALE: { label: 'Nữ', value: 'FEMALE' },
  OTHER: { label: 'Khác', value: 'OTHER' },
};

export const LOAI_DIEU_KIEN_AN_TOAN = {
  CO_LAP_DUONG_DAY: { code: 'CO_LAP_DUONG_DAY', label: 'Cô lập đường dây' },
  KHONG_CO_LAP_DUONG_DAY: { code: 'KHONG_CO_LAP_DUONG_DAY', label: 'Không cô lập đường dây' },
};

export const PHUONG_PHAP_THUC_HIEN = {
  BAY_TU_DONG: 'BAY_TU_DONG',
  BAY_THU_CONG: 'BAY_THU_CONG',
  KIEM_TRA_TRUYEN_THONG: 'KIEM_TRA_TRUYEN_THONG',
};

export const MACH = {
  MACH_DON: 'MACH_DON',
  MACH_KEP: 'MACH_KEP',
};

export const TEMPLATE_TYPE = {
  waypoint: 0,
  mapping2d: 1,
  mapping3d: 2,
  mappingStrip: 3
};
export const MIME_TYPES = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp',
};

export const RETURN_TYPE = { FULL: 'FULL', ID: 'ID' };

//Dji Cloud
export const DEVICE_DOMAIN = {
  DRONE: 0,
  PAYLOAD: 1,
  REMOTER_CONTROL: 2,
  DOCK: 3,
};

export const DEVICE_TYPE = {
  M350: 89,
  M300: 60,
  M30_OR_M3T_CAMERA: 67,
  M3E: 77,
  Z30: 20,
  XT2: 26,
  FPV: 39,
  XTS: 41,
  H20: 42,
  H20T: 43,
  P1: 50,
  M30_CAMERA: 52,
  M30T_CAMERA: 53,
  H20N: 61,
  DOCK_CAMERA: 165,
  L1: 90742,
  M3E_CAMERA: 66,
  M3M_CAMERA: 68,
  RC: 56,
  RC_PLUS: 119,
  RC_PRO: 144,
  DOCK: 1,
  DOCK2: 2,
  M3D: 91,
  M3D_CAMERA: 80,
  M3TD_CAMERA: 81,
};

export const DEVICE_SUB_TYPE = {
  ZERO: 0,
  ONE: 1,
  TWO: 2,
  _65535: 65535
};

export const DEVICE = {
  M350: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M350, sub_type: DEVICE_SUB_TYPE.ZERO },
  M300: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M300, sub_type: DEVICE_SUB_TYPE.ZERO },
  M30: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M30_OR_M3T_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  M30T: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M30_OR_M3T_CAMERA, sub_type: DEVICE_SUB_TYPE.ONE },
  M3E: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M3E, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3T: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M3E, sub_type: DEVICE_SUB_TYPE.ONE },
  M3M: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M3E, sub_type: DEVICE_SUB_TYPE.TWO },
  Z30: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.Z30, sub_type: DEVICE_SUB_TYPE.ZERO },
  XT2: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.XT2, sub_type: DEVICE_SUB_TYPE.ZERO },
  FPV: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.FPV, sub_type: DEVICE_SUB_TYPE.ZERO },
  XTS: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.XTS, sub_type: DEVICE_SUB_TYPE.ZERO },
  H20: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.H20, sub_type: DEVICE_SUB_TYPE.ZERO },
  H20T: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.H20T, sub_type: DEVICE_SUB_TYPE.ZERO },
  P1: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.P1, sub_type: DEVICE_SUB_TYPE._65535 },
  M30_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M30_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  M30T_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M30T_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  H20N: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.H20N, sub_type: DEVICE_SUB_TYPE.ZERO },
  DOCK_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.DOCK_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  L1: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.L1, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3E_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M3E_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3T_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M30_OR_M3T_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3M_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M3M_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  RC: { domain: DEVICE_DOMAIN.REMOTER_CONTROL, type: DEVICE_TYPE.RC, sub_type: DEVICE_SUB_TYPE.ZERO },
  RC_PLUS: { domain: DEVICE_DOMAIN.REMOTER_CONTROL, type: DEVICE_TYPE.RC_PLUS, sub_type: DEVICE_SUB_TYPE.ZERO },
  RC_PRO: { domain: DEVICE_DOMAIN.REMOTER_CONTROL, type: DEVICE_TYPE.RC_PRO, sub_type: DEVICE_SUB_TYPE.ZERO },
  DOCK: { domain: DEVICE_DOMAIN.DOCK, type: DEVICE_TYPE.DOCK, sub_type: DEVICE_SUB_TYPE.ZERO },
  DOCK2: { domain: DEVICE_DOMAIN.DOCK, type: DEVICE_TYPE.DOCK2, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3D: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M3D, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3TD: { domain: DEVICE_DOMAIN.DRONE, type: DEVICE_TYPE.M3D, sub_type: DEVICE_SUB_TYPE.ONE },
  M3D_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M3D_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  M3TD_CAMERA: { domain: DEVICE_DOMAIN.PAYLOAD, type: DEVICE_TYPE.M3TD_CAMERA, sub_type: DEVICE_SUB_TYPE.ZERO },
  find(domain, type, sub_type) { return Object.entries(this).find(device => device[1].domain === domain && device[1].type === type && device[1].sub_type === sub_type)?.[0] || 'UNKNOWN';},
};

export const ICON_URL = {
  SELECT_CAR: 'resource://pilot/drawable/tsa_car_select',
  NORMAL_CAR: 'resource://pilot/drawable/tsa_car_normal',
  SELECT_PERSON: 'resource://pilot/drawable/tsa_person_select',
  NORMAL_PERSON: 'resource://pilot/drawable/tsa_person_normal',
  SELECT_EQUIPMENT: 'resource://pilot/drawable/tsa_equipment_select',
  NORMAL_EQUIPMENT: 'resource://pilot/drawable/tsa_equipment_normal'
};

export const CLOUD_API_TOPIC = {
  STATUS: `^sys/product/[A-Za-z0-9]+/status$`,
  STATE: `^thing/product/[A-Za-z0-9]+/state$`,
  SERVICE_REPLY: `^thing/product/[A-Za-z0-9]+/services_reply$`,
  OSD: `^thing/product/[A-Za-z0-9]+/osd$`,
  REQUESTS: `^thing/product/[A-Za-z0-9]+/requests$`,
  EVENTS: `^thing/product/[A-Za-z0-9]+/events$`,
  PROPERTY_SET_REPLY: `^thing/product/[A-Za-z0-9]+/property/set_reply$`,
  DRC_UP: `^thing/product/[A-Za-z0-9]+/drc/up$`,
  UNKNOWN: '^.*$',
  find(pattern) {return Object.values(this).find(value => value && new RegExp(value).test(pattern)) || this.UNKNOWN;},
};

export const OSD_DEVICE_TYPE = {
  RC: { gateway: true, gateway_type: 'RC' },
  DOCK: { gateway: true, gateway_type: ['DOCK', 'DOCK2'] },
  RC_DRONE: { gateway: false, gateway_type: 'RC' },
  DOCK_DRONE: { gateway: false, gateway_type: ['DOCK', 'DOCK2'] },
  find(gatewayType, isGateway) {return Object.values(this).find(item => item.gateway_type.includes(gatewayType) && item.gateway === isGateway);}
};

export const PAYLOAD_POSITION = {
  FRONT_LEFT: 0,
  FRONT_RIGHT: 1,
  TOP: 2,
  FPV: 7
};

export function getAllModelWithPosition() {
  const position = Object.values(PAYLOAD_POSITION);
  const allModel = Object.values(DEVICE)
    .filter(device => DEVICE_DOMAIN.PAYLOAD === device.domain)
    .map(device => Object.keys(DEVICE).find(key => DEVICE[key] === device))
    .map(name => name.replace('_CAMERA', ''))
    .flatMap(m => position.map(p => m.concat('-').concat(p.toString())));
  return new Set(allModel);
}

export function getAllIndexWithPosition() {
  const position = Object.values(PAYLOAD_POSITION);
  const AllIndex = Object.values(DEVICE)
    .filter(device => DEVICE_DOMAIN.PAYLOAD === device.domain)
    .map(device => `${device.type}-${device.sub_type}`)
    .flatMap(m => position.map(p => m.concat('-').concat(p)));
  return new Set(AllIndex);
}

export const STATE_DATA_KEY = {
  RC_AND_DRONE_FIRMWARE_VERSION: { key: 'firmware_version', type: 'RC' },
  RC_LIVE_CAPACITY: { key: 'live_capacity', type: 'RC' },
  RC_DRONE_CONTROL_SOURCE: { key: 'control_source', type: 'RC' },
  RC_LIVE_STATUS: { key: 'live_status', type: 'RC' },
  RC_PAYLOAD_FIRMWARE: { key: getAllModelWithPosition(), type: 'RC' },

  DOCK_FIRMWARE_VERSION: { key: 'firmware_version', type: 'DOCK' },
  DOCK_LIVE_CAPACITY: { key: 'live_capacity', type: 'DOCK' },
  DOCK_DRONE_CONTROL_SOURCE: { key: 'control_source', type: 'DOCK' },
  DOCK_LIVE_STATUS: { key: 'live_status', type: 'DOCK' },
  DOCK_DRONE_WPMZ_VERSION: { key: 'wpmz_version', type: 'DOCK' },
  DOCK_DRONE_THERMAL_SUPPORTED_PALETTE_STYLE: { key: getAllIndexWithPosition(), type: 'DOCK' },
  DOCK_DRONE_RTH_MODE: { key: 'rth_mode', type: 'DOCK' },
  DOCK_DRONE_CURRENT_RTH_MODE: { key: 'current_rth_mode', type: 'DOCK' },
  DOCK_DRONE_COMMANDER_MODE_LOST_ACTION: { key: 'commander_mode_lost_action', type: 'DOCK' },
  DOCK_DRONE_CURRENT_COMMANDER_FLIGHT_MODE: { key: 'current_commander_flight_mode', type: 'DOCK' },
  DOCK_DRONE_COMMANDER_FLIGHT_HEIGHT: { key: 'commander_flight_height', type: 'DOCK' },
  DOCK_DRONE_MODE_CODE_REASON: { key: 'mode_code_reason', type: 'DOCK' },
  DOCK_DRONE_OFFLINE_MAP_ENABLE: { key: 'offline_map_enable', type: 'DOCK' },
  DOCK_AND_DRONE_DONGLE_INFOS: { key: 'dongle_infos', type: 'DOCK' },
  DOCK_SILENT_MODE: { key: 'silent_mode', type: 'DOCK' },
  UNKNOWN: { key: 'default', type: '' },

  find(keyEnum, type) {
    return Object.entries(this).find(([key, value]) => {
      return keyEnum.has(value.key) && value.type === type;
    })[1] || this.UNKNOWN;
  }
};

export const DEVICE_FIRMWARE_STATUS = {
  NOT_UPGRADE: 1,
  NORMAL_UPGRADE: 2,
  CONSISTENT_UPGRADE: 3,
  UPGRADING: 4,
  UNKNOWN: -1,
};

export const GATEWAY_TYPE = ['RC', 'RC_PLUS', 'RC_PRO', 'DOCK', 'DOCK2'];

