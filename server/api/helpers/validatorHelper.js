export const messageDefine = (key) => {
  return {
    'string.base': `${key} không đúng`,
    'number.base': `${key} không đúng`,
    'array.base': `${key} không đúng`,
    'string.empty': `${key} là không được bỏ trống`,
    'any.required': `${key} là không được thiếu`,
  };
};

const Joi = require('joi');

export function createValidatorSchema(objSchema, body, method) {
  body = body || {};

  if (method === 'POST') {
    // Với POST, sử dụng toàn bộ schema gốc
    return objSchema;
  } else {
    // Với các method khác, chỉ validate các field có trong body
    const schemaDescription = objSchema.describe();
    const schemaKeys = schemaDescription.keys || {};
    let newSchemaKeys = {};

    for (let key in schemaKeys) {
      if (body.hasOwnProperty(key)) {
        // Tạo lại schema cho field này, loại bỏ required constraint
        const fieldSchema = schemaKeys[key];
        if (fieldSchema.type === 'string') {
          newSchemaKeys[key] = Joi.string().messages(fieldSchema.messages || {});
        } else if (fieldSchema.type === 'number') {
          newSchemaKeys[key] = Joi.number().messages(fieldSchema.messages || {});
        } else if (fieldSchema.type === 'boolean') {
          newSchemaKeys[key] = Joi.boolean().messages(fieldSchema.messages || {});
        } else if (fieldSchema.type === 'array') {
          newSchemaKeys[key] = Joi.array().messages(fieldSchema.messages || {});
        } else {
          newSchemaKeys[key] = Joi.any().messages(fieldSchema.messages || {});
        }
      }
    }

    return Joi.object().keys(newSchemaKeys);
  }
}

export function validate(objSchema, data, method) {
  let schema = createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
