/* eslint consistent-return:0 import/order:0 */
import '@babel/polyfill';
import express from 'express';
import passport from 'passport';
import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
import request from 'request';

import logger from './logger';
import router from './router';
import routerDrone from './routerDrone';
import { mqttConnect } from './api/resources/DJICloud/Mqtt/mqtt.service';
import { redisConnect } from './api/resources/DJICloud/Redis/redis.service';

import argv from './argv';
import port from './port';
import { connect } from './config/db';
import { configJWTStrategy } from './api/middlewares/passport-jwt';
import { initNotificationService } from './api/resources/Notification/notification.service';
import '../server/api/common/prototype';
import i18next, { TFunction } from 'i18next';
import i18nextMiddleware from 'i18next-http-middleware';
import Backend from 'i18next-fs-backend';

const ApiMiddleware = require('./api-middleware');

import { enTranslation } from './locales/en/translation.js';
import { viTranslation } from './locales/vi/translation.js';

const resources = {
  en: {
    translation: enTranslation,
  },
  vi: {
    translation: viTranslation,
  },
};

i18next
  .use(Backend)
  .use(i18nextMiddleware.LanguageDetector)
  .init({
    debug: false,
    lng: 'vi',
    resources: resources,
    ns: ['translation'],
    defaultNS: 'translation',
    allowMultiLoading: true,
    fallbackLng: 'vi',
    preload: ['vi'],
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
  });

function startServer(isInitWorker) {
  console.log(`Worker ${process.pid} started`, 'INIT_WORKER', isInitWorker);
  connect(isInitWorker);
  mqttConnect();
  redisConnect();

  const app = express();

  app.use(i18nextMiddleware.handle(i18next));

  app.use(function(req, res, next) {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
  });
  app.use(bodyParser.json({ limit: '100mb' }));
  app.use(bodyParser.urlencoded({ limit: '100mb', extended: true, parameterLimit: 1000000 }));
  app.use(cookieParser());

// If you need a backend, e.g. an API, add your custom backend-specific middleware here
  app.use(ApiMiddleware());

  app.use('/api', router);
  app.use(routerDrone);

  app.use(passport.initialize());
  configJWTStrategy();

// get the intended host and port number, use localhost and port 8080 if not provided
  const customHost = argv.host || process.env.HOST;
  const host = customHost || null; // Let http.Server use its default IPv6/4 host
  const prettyHost = customHost || 'localhost';

// use the gzipped bundle
  app.get('*.js', (req, res, next) => {
    req.url = req.url + '.gz'; // eslint-disable-line
    res.set('Content-Encoding', 'gzip');
    next();
  });

// Start your app.
  const server = app.listen(port, host, async err => {
    if (err) {
      return logger.error(err.message);
    }
    logger.appStarted(port, prettyHost);
  });

  initNotificationService(server);
  const v8 = require('v8');
  const totalHeapSize = v8.getHeapStatistics().total_available_size;
  const totalHeapSizeGb = (totalHeapSize / 1024 / 1024 / 1024).toFixed(2);
  console.log('totalHeapSizeGb: ', totalHeapSizeGb);
}

function isInitWorker() {
  return process.env.INIT_WORKER;
}

exports.isInitWorker = isInitWorker;

if (process.env.NODE_ENV !== 'production') {
  startServer(true);
} else {
  const cluster = require('cluster');
  const os = require('os');
  const totalCPUs = os.cpus().length;

  // Tối ưu hóa số workers cho máy chủ POWER9
  // Giới hạn workers để tránh over-subscription và memory overhead
  const MAX_WORKERS = parseInt(process.env.MAX_WORKERS) || 28;
  const optimalWorkers = Math.min(totalCPUs, MAX_WORKERS);

  if (cluster.isMaster) {
    console.log(`Total CPUs: ${totalCPUs}, Using ${optimalWorkers} workers`);
    console.log(`Master ${process.pid} is running`);

    let initWorkerPID = 0;
    let isShuttingDown = false;
    const workers = new Map();

    function runInitWorker() {
      const firstWorker = cluster.fork({
        ...process.env,
        INIT_WORKER: true,
        WORKER_ID: 0,
      });
      initWorkerPID = firstWorker.process.pid;
      workers.set(firstWorker.process.pid, { worker: firstWorker, isInit: true });
      console.log(`Init worker ${firstWorker.process.pid} started`);
    }

    // Fork workers với giới hạn tối ưu
    for (let i = 0; i < optimalWorkers; i++) {
      if (i === 0) {
        runInitWorker();
      } else {
        const worker = cluster.fork({
          ...process.env,
          WORKER_ID: i,
        });
        workers.set(worker.process.pid, { worker: worker, isInit: false });
        console.log(`Worker ${worker.process.pid} started (ID: ${i})`);
      }
    }

    // Improved worker exit handling với retry logic
    cluster.on('exit', (worker, code, signal) => {
      const workerInfo = workers.get(worker.process.pid);
      console.log(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);

      // Log memory usage trước khi worker chết
      const memUsage = process.memoryUsage();
      console.log(`Memory usage before restart: RSS=${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap=${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);

      workers.delete(worker.process.pid);

      if (!isShuttingDown) {
        console.log('Restarting worker...');
        if (workerInfo && workerInfo.isInit) {
          runInitWorker();
        } else {
          const newWorker = cluster.fork({
            ...process.env,
            WORKER_ID: workers.size,
          });
          workers.set(newWorker.process.pid, { worker: newWorker, isInit: false });
        }
      }
    });

    // Graceful shutdown handling
    function gracefulShutdown(signal) {
      console.log(`Received ${signal}. Starting graceful shutdown...`);
      isShuttingDown = true;

      const shutdownTimeout = setTimeout(() => {
        console.log('Force killing all workers due to timeout');
        for (const [pid, workerInfo] of workers) {
          workerInfo.worker.kill('SIGKILL');
        }
        process.exit(1);
      }, 30000); // 30 seconds timeout

      // Gracefully close all workers
      for (const [pid, workerInfo] of workers) {
        workerInfo.worker.send('shutdown');
        workerInfo.worker.disconnect();
      }

      // Wait for all workers to exit
      const checkWorkers = setInterval(() => {
        if (workers.size === 0) {
          clearInterval(checkWorkers);
          clearTimeout(shutdownTimeout);
          console.log('All workers shut down gracefully');
          process.exit(0);
        }
      }, 100);
    }

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Monitor worker health
    setInterval(() => {
      const memUsage = process.memoryUsage();
      console.log(`Master process - Workers: ${workers.size}, Memory: RSS=${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap=${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
    }, 60000); // Log every minute

  } else {
    // Worker process
    const workerId = process.env.WORKER_ID || 'unknown';
    console.log(`Worker ${process.pid} (ID: ${workerId}) initializing...`);

    // Handle shutdown message from master
    process.on('message', (msg) => {
      if (msg === 'shutdown') {
        console.log(`Worker ${process.pid} received shutdown signal`);
        // Perform cleanup here
        process.exit(0);
      }
    });

    startServer(isInitWorker());
  }
}
