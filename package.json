{"name": "evnnpt-backend", "version": "1.0.0", "description": "", "engines": {"npm": ">=10", "node": ">=18.15.1"}, "author": "", "license": "UNLICENSED", "scripts": {"prebuild": "npm run build:clean", "build": "cross-env NODE_ENV=production webpack --config webpack/webpack.prod.babel.js --color -p --progress --hide-modules --display-optimization-bailout", "build:clean": "rimraf ./build", "start:server": "babel-node server", "start": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=development babel-watch server", "start:tunnel": "cross-env NODE_ENV=development ENABLE_TUNNEL=true babel-watch server", "prod": "npm run build && npm run buildServer && npm run start:prod", "start:prod": "cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production node build/server", "prettify": "prettier --write", "buildServer": "rimraf ./build/server && babel server -s -d build/server", "postinstall": "patch-package"}, "dependencies": {"@aws-sdk/client-s3": "^3.820.0", "@aws-sdk/client-sts": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.820.0", "@babel/polyfill": "7.4.3", "@koa/router": "10.0.0", "@xmldom/xmldom": "^0.9.8", "activedirectory": "0.7.2", "activedirectory2": "^2.2.0", "adm-zip": "^0.5.10", "base64topdf": "^1.1.8", "bcryptjs": "2.4.3", "busboy": "0.3.1", "carbone": "3.2.3", "chalk": "2.4.2", "commander": "8.0.0", "compression": "1.7.4", "connect-multiparty": "^2.2.0", "cookie-parser": "^1.4.7", "docximager": "^0.0.4", "exceljs": "^4.3.0", "exif-reader": "^1.0.3", "expo-server-sdk": "^3.6.0", "express": "^4.17.1", "fast-xml-parser": "3.19.0", "firebase-admin": "^11.9.0", "fs": "^0.0.1-security", "gm": "^1.23.1", "he": "1.2.0", "i18next": "^22.4.9", "i18next-fs-backend": "^2.1.1", "i18next-http-middleware": "^3.2.2", "immer": "^8.0.0", "install": "^0.13.0", "intl": "1.2.5", "invariant": "2.2.4", "ip": "1.1.5", "jsonwebtoken": "8.2.2", "jszip": "^3.10.1", "koa": "2.12.1", "koa-logger": "3.2.0", "ldap-authentication": "^3.3.4", "lodash": "^4.17.20", "minimist": "1.2.0", "minio": "^8.0.5", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "mongoose": "^5.11.8", "mongoose-paginate-v2": "1.3.9", "mongoose-unique-validator": "2.0.1", "mqtt": "^5.13.1", "node-cron": "^3.0.0", "node-fetch": "^2.6.1", "nodemailer": "^6.4.17", "passport": "0.4.0", "passport-jwt": "4.0.0", "patch-package": "^8.0.0", "path-to-regexp": "^6.2.2", "pdf-parse": "^1.1.1", "query-to-mongo": "^0.10.1", "redis": "^5.5.6", "request": "^2.88.2", "soap": "^0.43.0", "socket.io": "^4.1.2", "statuses": "2.0.0", "systeminformation": "^5.27.7", "uuid": "^11.1.0", "winston": "^3.7.2", "winston-daily-rotate-file": "^4.7.1", "winston-mongodb": "^5.0.7", "xlsx": "^0.16.9", "xml-js": "^1.6.11"}, "devDependencies": {"@babel/cli": "7.4.3", "@babel/core": "7.4.3", "@babel/node": "7.0.0", "@babel/plugin-proposal-class-properties": "7.4.0", "@babel/preset-env": "7.4.3", "@babel/register": "7.4.0", "babel-plugin-lodash": "3.3.4", "babel-watch": "^7.0.0", "cross-env": "5.2.0", "joi": "^17.4.0", "postinstall-postinstall": "^2.1.0", "rimraf": "2.6.3"}, "overrides": {"activedirectory@0.7.2": {"ldapjs": "2.3.3"}}}